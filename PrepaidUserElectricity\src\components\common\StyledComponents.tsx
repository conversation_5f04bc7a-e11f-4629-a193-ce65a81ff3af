import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
  TextInputProps,
  ViewProps,
  TextProps,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../../theme/ThemeContext';

// Card Component
interface CardProps extends ViewProps {
  children: React.ReactNode;
  elevation?: number;
}

export const Card: React.FC<CardProps> = ({ children, style, elevation = 2, ...props }) => {
  const { theme } = useTheme();
  
  return (
    <View
      style={[
        {
          backgroundColor: theme.colors.card,
          borderRadius: 8,
          padding: 16,
          marginVertical: 8,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: elevation,
          },
          shadowOpacity: 0.1,
          shadowRadius: elevation * 2,
          elevation: elevation,
        },
        style,
      ]}
      {...props}
    >
      {children}
    </View>
  );
};

// Gradient Button Component
interface GradientButtonProps extends TouchableOpacityProps {
  title: string;
  icon?: string;
  variant?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
}

export const GradientButton: React.FC<GradientButtonProps> = ({
  title,
  icon,
  variant = 'primary',
  size = 'medium',
  style,
  ...props
}) => {
  const { theme, font } = useTheme();
  
  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return { height: 36, paddingHorizontal: 16 };
      case 'large':
        return { height: 56, paddingHorizontal: 24 };
      default:
        return { height: 48, paddingHorizontal: 20 };
    }
  };

  const getFontSize = () => {
    switch (size) {
      case 'small':
        return font.sizes.small;
      case 'large':
        return font.sizes.large;
      default:
        return font.sizes.medium;
    }
  };

  const colors = variant === 'primary' 
    ? theme.colors.gradient 
    : [theme.colors.secondary, theme.colors.secondary];

  return (
    <TouchableOpacity style={[{ borderRadius: 8 }, style]} {...props}>
      <LinearGradient
        colors={colors}
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 8,
          },
          getButtonSize(),
        ]}
      >
        {icon && (
          <Icon
            name={icon}
            size={getFontSize() + 4}
            color="#fff"
            style={{ marginRight: title ? 8 : 0 }}
          />
        )}
        {title && (
          <Text
            style={{
              color: '#fff',
              fontSize: getFontSize(),
              fontWeight: font.weights.medium as any,
              fontFamily: font.fontFamily,
            }}
          >
            {title}
          </Text>
        )}
      </LinearGradient>
    </TouchableOpacity>
  );
};

// Themed Text Component
interface ThemedTextProps extends TextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'body' | 'caption';
  color?: 'primary' | 'secondary' | 'text' | 'textSecondary' | 'error' | 'warning' | 'success';
}

export const ThemedText: React.FC<ThemedTextProps> = ({
  variant = 'body',
  color = 'text',
  style,
  ...props
}) => {
  const { theme, font } = useTheme();
  
  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontFamily: font.fontFamily,
      color: theme.colors[color],
    };

    switch (variant) {
      case 'h1':
        return {
          ...baseStyle,
          fontSize: font.sizes.xxlarge,
          fontWeight: font.weights.bold as any,
        };
      case 'h2':
        return {
          ...baseStyle,
          fontSize: font.sizes.xlarge,
          fontWeight: font.weights.bold as any,
        };
      case 'h3':
        return {
          ...baseStyle,
          fontSize: font.sizes.large,
          fontWeight: font.weights.medium as any,
        };
      case 'caption':
        return {
          ...baseStyle,
          fontSize: font.sizes.small,
          fontWeight: font.weights.regular as any,
        };
      default:
        return {
          ...baseStyle,
          fontSize: font.sizes.medium,
          fontWeight: font.weights.regular as any,
        };
    }
  };

  return <Text style={[getTextStyle(), style]} {...props} />;
};

// Themed Text Input Component
interface ThemedTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  icon?: string;
}

export const ThemedTextInput: React.FC<ThemedTextInputProps> = ({
  label,
  error,
  icon,
  style,
  ...props
}) => {
  const { theme, font } = useTheme();
  
  return (
    <View style={{ marginVertical: 8 }}>
      {label && (
        <ThemedText variant="caption" color="textSecondary" style={{ marginBottom: 4 }}>
          {label}
        </ThemedText>
      )}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          borderWidth: 2,
          borderColor: error ? theme.colors.error : theme.colors.border,
          borderRadius: 8,
          backgroundColor: theme.colors.surface,
          paddingHorizontal: 12,
        }}
      >
        {icon && (
          <Icon
            name={icon}
            size={20}
            color={theme.colors.textSecondary}
            style={{ marginRight: 8 }}
          />
        )}
        <TextInput
          style={[
            {
              flex: 1,
              height: 48,
              fontSize: font.sizes.medium,
              fontFamily: font.fontFamily,
              color: theme.colors.text,
            },
            style,
          ]}
          placeholderTextColor={theme.colors.textSecondary}
          {...props}
        />
      </View>
      {error && (
        <ThemedText variant="caption" color="error" style={{ marginTop: 4 }}>
          {error}
        </ThemedText>
      )}
    </View>
  );
};

// Container Component
interface ContainerProps extends ViewProps {
  children: React.ReactNode;
  padding?: boolean;
}

export const Container: React.FC<ContainerProps> = ({
  children,
  padding = true,
  style,
  ...props
}) => {
  const { theme } = useTheme();
  
  return (
    <View
      style={[
        {
          flex: 1,
          backgroundColor: theme.colors.background,
          paddingHorizontal: padding ? 16 : 0,
          paddingVertical: padding ? 16 : 0,
        },
        style,
      ]}
      {...props}
    >
      {children}
    </View>
  );
};
