CMake Warning in src/main/cpp/worklets/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/x86/src/main/cpp/worklets/CMakeFiles/worklets.dir/./

  has 225 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in src/main/cpp/reanimated/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/x86/src/main/cpp/reanimated/CMakeFiles/reanimated.dir/./

  has 229 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


