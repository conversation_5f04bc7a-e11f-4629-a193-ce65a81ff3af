{"version": 3, "file": "config.js", "names": ["defaultConfig", "asyncUtilTimeout", "defaultIncludeHiddenElements", "concurrentRoot", "config", "configure", "options", "defaultHidden", "restOptions", "resetToDefaults", "getConfig"], "sources": ["../src/config.ts"], "sourcesContent": ["import type { DebugOptions } from './helpers/debug';\n\n/**\n * Global configuration options for React Native Testing Library.\n */\n\nexport type Config = {\n  /** Default timeout, in ms, for `waitFor` and `findBy*` queries. */\n  asyncUtilTimeout: number;\n\n  /** Default value for `includeHiddenElements` query option. */\n  defaultIncludeHiddenElements: boolean;\n\n  /** Default options for `debug` helper. */\n  defaultDebugOptions?: Partial<DebugOptions>;\n\n  /**\n   * Set to `false` to disable concurrent rendering.\n   * Otherwise `render` will default to concurrent rendering.\n   */\n  concurrentRoot: boolean;\n};\n\nexport type ConfigAliasOptions = {\n  /** RTL-compatibility alias to `defaultIncludeHiddenElements` */\n  defaultHidden: boolean;\n};\n\nconst defaultConfig: Config = {\n  asyncUtilTimeout: 1000,\n  defaultIncludeHiddenElements: false,\n  concurrentRoot: true,\n};\n\nlet config = { ...defaultConfig };\n\n/**\n * Configure global options for React Native Testing Library.\n */\nexport function configure(options: Partial<Config & ConfigAliasOptions>) {\n  const { defaultHidden, ...restOptions } = options;\n\n  const defaultIncludeHiddenElements =\n    restOptions.defaultIncludeHiddenElements ??\n    defaultHidden ??\n    config.defaultIncludeHiddenElements;\n\n  config = {\n    ...config,\n    ...restOptions,\n    defaultIncludeHiddenElements,\n  };\n}\n\nexport function resetToDefaults() {\n  config = { ...defaultConfig };\n}\n\nexport function getConfig() {\n  return config;\n}\n"], "mappings": ";;;;;;;;AAEA;AACA;AACA;;AAwBA,MAAMA,aAAqB,GAAG;EAC5BC,gBAAgB,EAAE,IAAI;EACtBC,4BAA4B,EAAE,KAAK;EACnCC,cAAc,EAAE;AAClB,CAAC;AAED,IAAIC,MAAM,GAAG;EAAE,GAAGJ;AAAc,CAAC;;AAEjC;AACA;AACA;AACO,SAASK,SAASA,CAACC,OAA6C,EAAE;EACvE,MAAM;IAAEC,aAAa;IAAE,GAAGC;EAAY,CAAC,GAAGF,OAAO;EAEjD,MAAMJ,4BAA4B,GAChCM,WAAW,CAACN,4BAA4B,IACxCK,aAAa,IACbH,MAAM,CAACF,4BAA4B;EAErCE,MAAM,GAAG;IACP,GAAGA,MAAM;IACT,GAAGI,WAAW;IACdN;EACF,CAAC;AACH;AAEO,SAASO,eAAeA,CAAA,EAAG;EAChCL,MAAM,GAAG;IAAE,GAAGJ;EAAc,CAAC;AAC/B;AAEO,SAASU,SAASA,CAAA,EAAG;EAC1B,OAAON,MAAM;AACf", "ignoreList": []}