# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    [gap of 30ms]
    exec-prefab 4202ms
    [gap of 134ms]
  generate-prefab-packages completed in 4366ms
  execute-generate-process
    [gap of 15ms]
    exec-configure 162040ms
    [gap of 2207ms]
  execute-generate-process completed in 164262ms
  [gap of 686ms]
  write-metadata-json-to-file 15ms
  [gap of 16ms]
generate_cxx_metadata completed in 169379ms

