{"version": 3, "file": "native-state.js", "names": ["nativeState", "exports", "valueForElement", "WeakMap", "contentOffsetForElement"], "sources": ["../src/native-state.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport type { Point } from './types';\n\n/**\n * Simulated native state for unmanaged controls.\n *\n * Values from `value` props (managed controls) should take precedence over these values.\n */\nexport type NativeState = {\n  valueForElement: WeakMap<ReactTestInstance, string>;\n  contentOffsetForElement: WeakMap<ReactTestInstance, Point>;\n};\n\nexport const nativeState: NativeState = {\n  valueForElement: new WeakMap(),\n  contentOffsetForElement: new WeakMap(),\n};\n"], "mappings": ";;;;;;AAIA;AACA;AACA;AACA;AACA;;AAMO,MAAMA,WAAwB,GAAAC,OAAA,CAAAD,WAAA,GAAG;EACtCE,eAAe,EAAE,IAAIC,OAAO,CAAC,CAAC;EAC9BC,uBAAuB,EAAE,IAAID,OAAO,CAAC;AACvC,CAAC", "ignoreList": []}