{"version": 3, "file": "types.js", "names": [], "sources": ["../../src/matchers/types.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON> } from 'react-native';\nimport type { ReactTestInstance } from 'react-test-renderer';\n\nimport type { AccessibilityValueMatcher } from '../helpers/matchers/match-accessibility-value';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport type { Style } from './to-have-style';\n\nexport interface JestNativeMatchers<R> {\n  /**\n   * Assert whether a host element is present in the element tree (screen) or not.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobeonthescreen)\n   *\n   * @example\n   * <Text>Hello</Text>\n   *\n   * expect(getByText('Hello')).toBeOnTheScreen()\n   * expect(queryByText('Other')).not.toBeOnTheScreen()\n   */\n  toBeOnTheScreen(): R;\n\n  /**\n   * Assert whether a host element is checked based on accessibility props.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobechecked)\n   *\n   * @see {@link toBePartiallyChecked} for a related matcher.\n   *\n   * @example\n   * <View accessible role=\"checkbox\" aria-checked aria-label=\"Enable\" />\n   *\n   * expect(getByRole('checkbox', { name: \"Enable\" })).toBeChecked()\n   */\n  toBeChecked(): R;\n\n  /**\n   * Assert whether a host element is collapsed based on accessibility props.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobeexpanded)\n   *\n   * @see {@link toBeExpanded} for an inverse matcher.\n   *\n   * @example\n   * <View testID=\"details\" aria-expanded={false} />\n   *\n   * expect(getByTestId('details').toBeCollapsed()\n   */\n  toBeCollapsed(): R;\n\n  /**\n   * Assert whether a host element is disabled based on accessibility props.\n   *\n   * This matcher will check ancestor elements for their disabled state as well.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobeenabled)\n   *\n   * @see {@link toBeEnabled} for an inverse matcher.\n   *\n   * @example\n   * <View role=\"button\" aria-disabled />\n   *\n   * expect(getByRole('button').toBeDisabled()\n   *\n   */\n  toBeDisabled(): R;\n\n  /**\n   * Assert whether a host element is busy based on accessibility props.\n   *\n   * This matcher will check ancestor elements for their disabled state as well.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobebusy)\n   *\n   * @example\n   * <View testID=\"loader\" aria-busy />\n   *\n   * expect(getByTestId('loader')).toBeBusy()\n   */\n  toBeBusy(): R;\n\n  /**\n   * Assert whether a host element has no host children or text content.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobeemptyelement)\n   *\n   * @example\n   * <View testID=\"not-empty\">\n   *   <View testID=\"empty\" />\n   * </View>\n   *\n   * expect(getByTestId('empty')).toBeEmptyElement()\n   * expect(getByTestId('not-mepty')).not.toBeEmptyElement()\n   */\n  toBeEmptyElement(): R;\n\n  /**\n   * Assert whether a host element is enabled based on accessibility props.\n   *\n   * This matcher will check ancestor elements for their disabled state as well.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobeenabled)\n   *\n   * @see {@link toBeDisabled} for inverse matcher.\n   *\n   * @example\n   * <View role=\"button\" aria-disabled={false} />\n   *\n   * expect(getByRole('button').toBeEnabled()\n   */\n  toBeEnabled(): R;\n\n  /**\n   * Assert whether a host element is expanded based on accessibility props.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobeexpanded)\n   *\n   * @see {@link toBeCollapsed} for inverse matcher.\n   *\n   * @example\n   * <View testID=\"details\" aria-expanded />\n   *\n   * expect(getByTestId('details').toBeExpanded()\n   */\n  toBeExpanded(): R;\n\n  /**\n   * Assert whether a host element is partially checked based on accessibility props.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobechecked)\n   *\n   * @see {@link toBeChecked} for related matcher.\n   *\n   * @example\n   * <View accessible role=\"checkbox\" aria-checked=\"mixed\" aria-label=\"Enable\" />\n   *\n   * expect(getByRole('checkbox', { name: \"Enable\" })).toBePartiallyChecked()\n   */\n  toBePartiallyChecked(): R;\n\n  /**\n   * Assert whether a host element is selected based on accessibility props.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobeselected)\n   *\n   * @example\n   * <View testID=\"view\" aria-selected />\n   *\n   * expect(getByTestId('view')).toBeSelected()\n   */\n  toBeSelected(): R;\n\n  /**\n   * Assert whether a host element is visible based on style and accessibility props.\n   *\n   * This matcher will check ancestor elements for their visibility as well.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tobevisible)\n   *\n   * @example\n   * <View testID=\"visible\" />\n   * <View testID=\"not-visible\" style={{ display: 'none' }} />\n   *\n   * expect(getByTestId('visible')).toBeVisible()\n   * expect(getByTestId('not-visible')).not.toBeVisible()\n   */\n  toBeVisible(): R;\n\n  /**\n   * Assert whether a host element contains another host element.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tocontainelement)\n   *\n   * @example\n   * <View testID=\"outer\">\n   *   <View testID=\"inner\" />\n   * </View>\n   *\n   * expect(getByTestId('outer')).toContainElement(getByTestId('inner'));\n   */\n  toContainElement(element: ReactTestInstance | null): R;\n\n  /**\n   * Assert whether a host element has a given accessbility value.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tohaveaccessibilityvalue)\n   *\n   *\n   * @example\n   * <View testID=\"view\" aria-valuetext=\"33%\" />\n   *\n   * expect(getByTestId('view')).toHaveAccessibilityValue({ text: '33%' });\n   */\n  toHaveAccessibilityValue(expectedValue: AccessibilityValueMatcher): R;\n\n  /**\n   * Assert whether a host element has a given accessibile name based on the accessibility label or text content.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tohaveaccessiblename)\n   *\n   * @example\n   * <View testID=\"view\" aria-label=\"Hello\" />\n   *\n   * expect(getByTestId('view')).toHaveAccessibleName('Hello');\n   */\n  toHaveAccessibleName(expectedName?: TextMatch, options?: TextMatchOptions): R;\n\n  /**\n   * Assert whether a host `TextInput` element has a given display value based on `value` prop, unmanaged native state, and `defaultValue` prop.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tohavedisplayvalue)\n   *\n   * @example\n   * <TextInput testID=\"input\" value=\"Hello\" />\n   *\n   * expect(getByTestId('input')).toHaveDisplayValue('Hello');\n   */\n  toHaveDisplayValue(expectedValue: TextMatch, options?: TextMatchOptions): R;\n\n  /**\n   * Assert whether a host element has a given prop.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tohaveprop)\n   *\n   * @example\n   * <Text testID=\"text\" numberOfLines={1]} />\n   *\n   * expect(getByTestId('text')).toHaveProp('numberOfLines');\n   * expect(getByTestId('text')).toHaveProp('numberOfLines', 1);\n   */\n  toHaveProp(name: string, expectedValue?: unknown): R;\n\n  /**\n   * Assert whether a host element has a given style.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tohavestyle)\n   *\n   * @example\n   * <View testID=\"view\" style={{ width: '100%' }} />\n   *\n   * expect(getByTestId('view')).toHaveStyle({ width: '100%' });\n   * expect(getByTestId('view')).not.toHaveStyle({ width: '50%' });\n   */\n  toHaveStyle(style: StyleProp<Style>): R;\n\n  /**\n   * Assert whether a host element has a given text content.\n   *\n   * @see\n   * [Jest Matchers docs](https://callstack.github.io/react-native-testing-library/docs/jest-matchers#tohavetextcontent)\n   *\n   * @example\n   * <View testID=\"view\">\n   *   <Text>Hello World</Text>\n   * </View>\n   *\n   * expect(getByTestId('view')).toHaveTextContent('Hello World');\n   * expect(getByTestId('view')).toHaveTextContent('Hello', { exact: false }});\n   * expect(getByTestId('view')).toHaveTextContent(/hello/i);\n   * expect(getByTestId('view')).not.toHaveTextContent('Hello');\n   */\n  toHaveTextContent(expectedText: TextMatch, options?: TextMatchOptions): R;\n}\n\n// Implicit Jest global `expect`.\ndeclare global {\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  namespace jest {\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type, @typescript-eslint/no-unused-vars\n    interface Matchers<R, T = {}> extends JestNativeMatchers<R> {}\n  }\n}\n\n// Explicit `@jest/globals` `expect` matchers.\n// @ts-expect-error: Invalid module name in augmentation, module '@jest/expect' cannot be found\ndeclare module '@jest/expect' {\n  // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n  interface Matchers<R extends void | Promise<void>> extends JestNativeMatchers<R> {}\n}\n"], "mappings": "", "ignoreList": []}