{"version": 3, "file": "index.js", "names": ["require", "_act", "_flushMicroTasks", "_pure", "Object", "keys", "for<PERSON>ach", "key", "exports", "defineProperty", "enumerable", "get", "process", "env", "RNTL_SKIP_AUTO_CLEANUP", "after<PERSON>ach", "flushMicroTasks", "cleanup", "beforeAll", "afterAll", "previousIsReactActEnvironment", "getIsReactActEnvironment", "setReactActEnvironment"], "sources": ["../src/index.ts"], "sourcesContent": ["import './helpers/ensure-peer-deps';\nimport './matchers/extend-expect';\n\nimport { getIsReactActEnvironment, setReactActEnvironment } from './act';\nimport { flushMicroTasks } from './flush-micro-tasks';\nimport { cleanup } from './pure';\n\nif (!process?.env?.RNTL_SKIP_AUTO_CLEANUP) {\n  // If we're running in a test runner that supports afterEach\n  // then we'll automatically run cleanup afterEach test\n  // this ensures that tests run in isolation from each other\n  // if you don't like this then either import the `pure` module\n  // or set the RNTL_SKIP_AUTO_CLEANUP env variable to 'true'.\n  if (typeof afterEach === 'function') {\n    afterEach(async () => {\n      await flushMicroTasks();\n      cleanup();\n    });\n  }\n\n  if (typeof beforeAll === 'function' && typeof afterAll === 'function') {\n    // This matches the behavior of React < 18.\n    let previousIsReactActEnvironment = getIsReactActEnvironment();\n    beforeAll(() => {\n      previousIsReactActEnvironment = getIsReactActEnvironment();\n      setReactActEnvironment(true);\n    });\n\n    afterAll(() => {\n      setReactActEnvironment(previousIsReactActEnvironment);\n    });\n  }\n}\n\nexport * from './pure';\n"], "mappings": ";;;;;AAAAA,OAAA;AACAA,OAAA;AAEA,IAAAC,IAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AA6BAI,MAAA,CAAAC,IAAA,CAAAF,KAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAA,GAAA,IAAAC,OAAA,IAAAA,OAAA,CAAAD,GAAA,MAAAJ,KAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAK,cAAA,CAAAD,OAAA,EAAAD,GAAA;IAAAG,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAR,KAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AA3BA,IAAI,CAACK,OAAO,EAAEC,GAAG,EAAEC,sBAAsB,EAAE;EACzC;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;IACnCA,SAAS,CAAC,YAAY;MACpB,MAAM,IAAAC,gCAAe,EAAC,CAAC;MACvB,IAAAC,aAAO,EAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA,IAAI,OAAOC,SAAS,KAAK,UAAU,IAAI,OAAOC,QAAQ,KAAK,UAAU,EAAE;IACrE;IACA,IAAIC,6BAA6B,GAAG,IAAAC,6BAAwB,EAAC,CAAC;IAC9DH,SAAS,CAAC,MAAM;MACdE,6BAA6B,GAAG,IAAAC,6BAAwB,EAAC,CAAC;MAC1D,IAAAC,2BAAsB,EAAC,IAAI,CAAC;IAC9B,CAAC,CAAC;IAEFH,QAAQ,CAAC,MAAM;MACb,IAAAG,2BAAsB,EAACF,6BAA6B,CAAC;IACvD,CAAC,CAAC;EACJ;AACF", "ignoreList": []}