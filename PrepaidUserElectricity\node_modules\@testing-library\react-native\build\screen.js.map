{"version": 3, "file": "screen.js", "names": ["SCREEN_ERROR", "notImplemented", "Error", "notImplementedDebug", "defaultScreen", "isDetached", "root", "UNSAFE_root", "debug", "update", "unmount", "rerender", "toJSON", "getByLabelText", "getAllByLabelText", "queryByLabelText", "queryAllByLabelText", "findByLabelText", "findAllByLabelText", "getByHintText", "getAllByHintText", "queryByHintText", "queryAllByHintText", "findByHintText", "findAllByHintText", "getByA11yHint", "getAllByA11yHint", "queryByA11yHint", "queryAllByA11yHint", "findByA11yHint", "findAllByA11yHint", "getByAccessibilityHint", "getAllByAccessibilityHint", "queryByAccessibilityHint", "queryAllByAccessibilityHint", "findByAccessibilityHint", "findAllByAccessibilityHint", "getByRole", "getAllByRole", "queryByRole", "queryAllByRole", "findByRole", "findAllByRole", "UNSAFE_getByProps", "UNSAFE_getAllByProps", "UNSAFE_queryByProps", "UNSAFE_queryAllByProps", "UNSAFE_getByType", "UNSAFE_getAllByType", "UNSAFE_queryByType", "UNSAFE_queryAllByType", "getByPlaceholderText", "getAllByPlaceholderText", "queryByPlaceholderText", "queryAllByPlaceholderText", "findByPlaceholderText", "findAllByPlaceholderText", "getByDisplayValue", "getAllByDisplayValue", "queryByDisplayValue", "queryAllByDisplayValue", "findByDisplayValue", "findAllByDisplayValue", "getByTestId", "getAllByTestId", "queryByTestId", "queryAllByTestId", "findByTestId", "findAllByTestId", "getByText", "getAllByText", "queryByText", "queryAllByText", "findByText", "findAllByText", "screen", "exports", "setRenderResult", "renderResult", "clearRenderResult"], "sources": ["../src/screen.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport type { RenderResult } from './render';\n\nconst SCREEN_ERROR = '`render` method has not been called';\n\nconst notImplemented = () => {\n  throw new Error(SCREEN_ERROR);\n};\n\nconst notImplementedDebug = () => {\n  throw new Error(SCREEN_ERROR);\n};\n\ninterface Screen extends RenderResult {\n  isDetached?: boolean;\n}\n\nconst defaultScreen: Screen = {\n  isDetached: true,\n  get root(): ReactTestInstance {\n    throw new Error(SCREEN_ERROR);\n  },\n  get UNSAFE_root(): ReactTestInstance {\n    throw new Error(SCREEN_ERROR);\n  },\n  debug: notImplementedDebug,\n  update: notImplemented,\n  unmount: notImplemented,\n  rerender: notImplemented,\n  toJSON: notImplemented,\n  getByLabelText: notImplemented,\n  getAllByLabelText: notImplemented,\n  queryByLabelText: notImplemented,\n  queryAllByLabelText: notImplemented,\n  findByLabelText: notImplemented,\n  findAllByLabelText: notImplemented,\n  getByHintText: notImplemented,\n  getAllByHintText: notImplemented,\n  queryByHintText: notImplemented,\n  queryAllByHintText: notImplemented,\n  findByHintText: notImplemented,\n  findAllByHintText: notImplemented,\n  getByA11yHint: notImplemented,\n  getAllByA11yHint: notImplemented,\n  queryByA11yHint: notImplemented,\n  queryAllByA11yHint: notImplemented,\n  findByA11yHint: notImplemented,\n  findAllByA11yHint: notImplemented,\n  getByAccessibilityHint: notImplemented,\n  getAllByAccessibilityHint: notImplemented,\n  queryByAccessibilityHint: notImplemented,\n  queryAllByAccessibilityHint: notImplemented,\n  findByAccessibilityHint: notImplemented,\n  findAllByAccessibilityHint: notImplemented,\n  getByRole: notImplemented,\n  getAllByRole: notImplemented,\n  queryByRole: notImplemented,\n  queryAllByRole: notImplemented,\n  findByRole: notImplemented,\n  findAllByRole: notImplemented,\n  UNSAFE_getByProps: notImplemented,\n  UNSAFE_getAllByProps: notImplemented,\n  UNSAFE_queryByProps: notImplemented,\n  UNSAFE_queryAllByProps: notImplemented,\n  UNSAFE_getByType: notImplemented,\n  UNSAFE_getAllByType: notImplemented,\n  UNSAFE_queryByType: notImplemented,\n  UNSAFE_queryAllByType: notImplemented,\n  getByPlaceholderText: notImplemented,\n  getAllByPlaceholderText: notImplemented,\n  queryByPlaceholderText: notImplemented,\n  queryAllByPlaceholderText: notImplemented,\n  findByPlaceholderText: notImplemented,\n  findAllByPlaceholderText: notImplemented,\n  getByDisplayValue: notImplemented,\n  getAllByDisplayValue: notImplemented,\n  queryByDisplayValue: notImplemented,\n  queryAllByDisplayValue: notImplemented,\n  findByDisplayValue: notImplemented,\n  findAllByDisplayValue: notImplemented,\n  getByTestId: notImplemented,\n  getAllByTestId: notImplemented,\n  queryByTestId: notImplemented,\n  queryAllByTestId: notImplemented,\n  findByTestId: notImplemented,\n  findAllByTestId: notImplemented,\n  getByText: notImplemented,\n  getAllByText: notImplemented,\n  queryByText: notImplemented,\n  queryAllByText: notImplemented,\n  findByText: notImplemented,\n  findAllByText: notImplemented,\n};\n\nexport let screen: Screen = defaultScreen;\n\nexport function setRenderResult(renderResult: RenderResult) {\n  screen = renderResult;\n}\n\nexport function clearRenderResult() {\n  screen = defaultScreen;\n}\n"], "mappings": ";;;;;;;;AAIA,MAAMA,YAAY,GAAG,qCAAqC;AAE1D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAM,IAAIC,KAAK,CAACF,YAAY,CAAC;AAC/B,CAAC;AAED,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;EAChC,MAAM,IAAID,KAAK,CAACF,YAAY,CAAC;AAC/B,CAAC;AAMD,MAAMI,aAAqB,GAAG;EAC5BC,UAAU,EAAE,IAAI;EAChB,IAAIC,IAAIA,CAAA,EAAsB;IAC5B,MAAM,IAAIJ,KAAK,CAACF,YAAY,CAAC;EAC/B,CAAC;EACD,IAAIO,WAAWA,CAAA,EAAsB;IACnC,MAAM,IAAIL,KAAK,CAACF,YAAY,CAAC;EAC/B,CAAC;EACDQ,KAAK,EAAEL,mBAAmB;EAC1BM,MAAM,EAAER,cAAc;EACtBS,OAAO,EAAET,cAAc;EACvBU,QAAQ,EAAEV,cAAc;EACxBW,MAAM,EAAEX,cAAc;EACtBY,cAAc,EAAEZ,cAAc;EAC9Ba,iBAAiB,EAAEb,cAAc;EACjCc,gBAAgB,EAAEd,cAAc;EAChCe,mBAAmB,EAAEf,cAAc;EACnCgB,eAAe,EAAEhB,cAAc;EAC/BiB,kBAAkB,EAAEjB,cAAc;EAClCkB,aAAa,EAAElB,cAAc;EAC7BmB,gBAAgB,EAAEnB,cAAc;EAChCoB,eAAe,EAAEpB,cAAc;EAC/BqB,kBAAkB,EAAErB,cAAc;EAClCsB,cAAc,EAAEtB,cAAc;EAC9BuB,iBAAiB,EAAEvB,cAAc;EACjCwB,aAAa,EAAExB,cAAc;EAC7ByB,gBAAgB,EAAEzB,cAAc;EAChC0B,eAAe,EAAE1B,cAAc;EAC/B2B,kBAAkB,EAAE3B,cAAc;EAClC4B,cAAc,EAAE5B,cAAc;EAC9B6B,iBAAiB,EAAE7B,cAAc;EACjC8B,sBAAsB,EAAE9B,cAAc;EACtC+B,yBAAyB,EAAE/B,cAAc;EACzCgC,wBAAwB,EAAEhC,cAAc;EACxCiC,2BAA2B,EAAEjC,cAAc;EAC3CkC,uBAAuB,EAAElC,cAAc;EACvCmC,0BAA0B,EAAEnC,cAAc;EAC1CoC,SAAS,EAAEpC,cAAc;EACzBqC,YAAY,EAAErC,cAAc;EAC5BsC,WAAW,EAAEtC,cAAc;EAC3BuC,cAAc,EAAEvC,cAAc;EAC9BwC,UAAU,EAAExC,cAAc;EAC1ByC,aAAa,EAAEzC,cAAc;EAC7B0C,iBAAiB,EAAE1C,cAAc;EACjC2C,oBAAoB,EAAE3C,cAAc;EACpC4C,mBAAmB,EAAE5C,cAAc;EACnC6C,sBAAsB,EAAE7C,cAAc;EACtC8C,gBAAgB,EAAE9C,cAAc;EAChC+C,mBAAmB,EAAE/C,cAAc;EACnCgD,kBAAkB,EAAEhD,cAAc;EAClCiD,qBAAqB,EAAEjD,cAAc;EACrCkD,oBAAoB,EAAElD,cAAc;EACpCmD,uBAAuB,EAAEnD,cAAc;EACvCoD,sBAAsB,EAAEpD,cAAc;EACtCqD,yBAAyB,EAAErD,cAAc;EACzCsD,qBAAqB,EAAEtD,cAAc;EACrCuD,wBAAwB,EAAEvD,cAAc;EACxCwD,iBAAiB,EAAExD,cAAc;EACjCyD,oBAAoB,EAAEzD,cAAc;EACpC0D,mBAAmB,EAAE1D,cAAc;EACnC2D,sBAAsB,EAAE3D,cAAc;EACtC4D,kBAAkB,EAAE5D,cAAc;EAClC6D,qBAAqB,EAAE7D,cAAc;EACrC8D,WAAW,EAAE9D,cAAc;EAC3B+D,cAAc,EAAE/D,cAAc;EAC9BgE,aAAa,EAAEhE,cAAc;EAC7BiE,gBAAgB,EAAEjE,cAAc;EAChCkE,YAAY,EAAElE,cAAc;EAC5BmE,eAAe,EAAEnE,cAAc;EAC/BoE,SAAS,EAAEpE,cAAc;EACzBqE,YAAY,EAAErE,cAAc;EAC5BsE,WAAW,EAAEtE,cAAc;EAC3BuE,cAAc,EAAEvE,cAAc;EAC9BwE,UAAU,EAAExE,cAAc;EAC1ByE,aAAa,EAAEzE;AACjB,CAAC;AAEM,IAAI0E,MAAc,GAAAC,OAAA,CAAAD,MAAA,GAAGvE,aAAa;AAElC,SAASyE,eAAeA,CAACC,YAA0B,EAAE;EAC1DF,OAAA,CAAAD,MAAA,GAAAA,MAAM,GAAGG,YAAY;AACvB;AAEO,SAASC,iBAAiBA,CAAA,EAAG;EAClCH,OAAA,CAAAD,MAAA,GAAAA,MAAM,GAAGvE,aAAa;AACxB", "ignoreList": []}