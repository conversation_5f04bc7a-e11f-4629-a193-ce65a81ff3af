import React from 'react';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../../theme/ThemeContext';

export type IconName = 
  | 'dashboard'
  | 'purchases'
  | 'usage'
  | 'history'
  | 'settings'
  | 'currency'
  | 'units'
  | 'warning'
  | 'success'
  | 'error'
  | 'info'
  | 'notification'
  | 'chart'
  | 'calendar'
  | 'time'
  | 'reset'
  | 'theme'
  | 'font'
  | 'export'
  | 'share';

interface AppIconProps {
  name: IconName;
  size?: number;
  color?: string;
  style?: any;
}

const iconMapping: Record<IconName, string> = {
  dashboard: 'dashboard',
  purchases: 'shopping-cart',
  usage: 'trending-up',
  history: 'history',
  settings: 'settings',
  currency: 'attach-money',
  units: 'flash-on',
  warning: 'warning',
  success: 'check-circle',
  error: 'error',
  info: 'info',
  notification: 'notifications',
  chart: 'bar-chart',
  calendar: 'calendar-today',
  time: 'schedule',
  reset: 'restore',
  theme: 'palette',
  font: 'text-fields',
  export: 'file-download',
  share: 'share',
};

const AppIcon: React.FC<AppIconProps> = ({ 
  name, 
  size = 24, 
  color, 
  style 
}) => {
  const { theme } = useTheme();
  const iconColor = color || theme.colors.text;
  const materialIconName = iconMapping[name] || 'help';
  
  return (
    <Icon 
      name={materialIconName} 
      size={size} 
      color={iconColor} 
      style={style}
    />
  );
};

// Predefined icon components for common use cases
export const DashboardIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="dashboard" {...props} />
);

export const PurchasesIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="purchases" {...props} />
);

export const UsageIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="usage" {...props} />
);

export const HistoryIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="history" {...props} />
);

export const SettingsIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="settings" {...props} />
);

export const CurrencyIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="currency" {...props} />
);

export const UnitsIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="units" {...props} />
);

export const WarningIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="warning" {...props} />
);

export const SuccessIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="success" {...props} />
);

export const ErrorIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="error" {...props} />
);

export const InfoIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="info" {...props} />
);

export const NotificationIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="notification" {...props} />
);

export const ChartIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="chart" {...props} />
);

export const CalendarIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="calendar" {...props} />
);

export const TimeIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="time" {...props} />
);

export const ResetIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="reset" {...props} />
);

export const ThemeIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="theme" {...props} />
);

export const FontIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="font" {...props} />
);

export const ExportIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="export" {...props} />
);

export const ShareIcon: React.FC<Omit<AppIconProps, 'name'>> = (props) => (
  <AppIcon name="share" {...props} />
);

export default AppIcon;
