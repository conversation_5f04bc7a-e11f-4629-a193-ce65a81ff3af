import React from 'react';
import { View, ViewStyle } from 'react-native';
import Svg, { 
  Path, 
  Defs, 
  LinearGradient, 
  Stop, 
  Circle,
  Polygon 
} from 'react-native-svg';
import { useTheme } from '../../theme/ThemeContext';
import { ThemedText } from './StyledComponents';

interface AppLogoProps {
  size?: number;
  showText?: boolean;
  style?: ViewStyle;
}

const AppLogo: React.FC<AppLogoProps> = ({ 
  size = 80, 
  showText = true, 
  style 
}) => {
  const { theme, font } = useTheme();
  
  return (
    <View style={[{ alignItems: 'center' }, style]}>
      {/* Lightning Logo */}
      <Svg width={size} height={size} viewBox="0 0 100 100">
        <Defs>
          <LinearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={theme.colors.primary} />
            <Stop offset="100%" stopColor={theme.colors.primaryDark} />
          </LinearGradient>
          <LinearGradient id="sparkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={theme.colors.secondary} />
            <Stop offset="100%" stopColor={theme.colors.warning} />
          </LinearGradient>
        </Defs>
        
        {/* Background Circle */}
        <Circle
          cx="50"
          cy="50"
          r="45"
          fill="url(#logoGradient)"
          stroke={theme.colors.surface}
          strokeWidth="2"
        />
        
        {/* Lightning Bolt */}
        <Path
          d="M35 25 L55 25 L45 45 L60 45 L40 75 L50 50 L35 50 Z"
          fill={theme.colors.surface}
          stroke={theme.colors.primaryDark}
          strokeWidth="1"
        />
        
        {/* Energy Sparks */}
        <Polygon
          points="25,35 30,30 35,35 30,40"
          fill="url(#sparkGradient)"
        />
        <Polygon
          points="70,25 75,20 80,25 75,30"
          fill="url(#sparkGradient)"
        />
        <Polygon
          points="75,65 80,60 85,65 80,70"
          fill="url(#sparkGradient)"
        />
        <Polygon
          points="20,65 25,60 30,65 25,70"
          fill="url(#sparkGradient)"
        />
      </Svg>
      
      {/* App Name */}
      {showText && (
        <View style={{ marginTop: 8, alignItems: 'center' }}>
          <ThemedText 
            variant="h3" 
            color="primary"
            style={{ 
              fontWeight: font.weights.bold as any,
              textAlign: 'center',
              letterSpacing: 1,
            }}
          >
            PREPAID USER
          </ThemedText>
          <ThemedText 
            variant="body" 
            color="textSecondary"
            style={{ 
              fontWeight: font.weights.medium as any,
              textAlign: 'center',
              letterSpacing: 0.5,
            }}
          >
            ELECTRICITY
          </ThemedText>
        </View>
      )}
    </View>
  );
};

export default AppLogo;
