import React from 'react';
import { View, Dimensions } from 'react-native';
import {
  Victory<PERSON><PERSON>,
  VictoryBar,
  VictoryAxis,
  VictoryTheme,
  VictoryTooltip,
  VictoryLegend,
} from 'victory-native';
import { useTheme } from '../../theme/ThemeContext';
import { ThemedText } from '../common/StyledComponents';

interface ComparisonDataPoint {
  x: string;
  y: number;
  label?: string;
}

interface ComparisonChartProps {
  purchaseData: ComparisonDataPoint[];
  usageData: ComparisonDataPoint[];
  title?: string;
  height?: number;
}

const ComparisonChart: React.FC<ComparisonChartProps> = ({
  purchaseData,
  usageData,
  title = 'Purchases vs Usage',
  height = 250,
}) => {
  const { theme } = useTheme();
  const screenWidth = Dimensions.get('window').width;
  
  if (purchaseData.length === 0 && usageData.length === 0) {
    return (
      <View style={{ height, justifyContent: 'center', alignItems: 'center' }}>
        <ThemedText color="textSecondary">No data available</ThemedText>
      </View>
    );
  }
  
  return (
    <View>
      {title && (
        <ThemedText variant="h3" style={{ marginBottom: 8, textAlign: 'center' }}>
          {title}
        </ThemedText>
      )}
      
      <VictoryChart
        theme={VictoryTheme.material}
        width={screenWidth - 64}
        height={height}
        padding={{ left: 60, top: 40, right: 40, bottom: 60 }}
        domainPadding={{ x: 40 }}
      >
        <VictoryLegend
          x={50}
          y={10}
          orientation="horizontal"
          gutter={20}
          style={{
            border: { stroke: theme.colors.border, fill: theme.colors.surface },
            title: { fontSize: 14, fill: theme.colors.text },
            labels: { fontSize: 12, fill: theme.colors.text },
          }}
          data={[
            { name: 'Purchases', symbol: { fill: theme.colors.secondary } },
            { name: 'Usage Cost', symbol: { fill: theme.colors.primary } },
          ]}
        />
        
        <VictoryAxis
          dependentAxis
          style={{
            axis: { stroke: theme.colors.border },
            tickLabels: { fill: theme.colors.textSecondary, fontSize: 12 },
            grid: { stroke: theme.colors.border, strokeOpacity: 0.3 },
          }}
        />
        <VictoryAxis
          style={{
            axis: { stroke: theme.colors.border },
            tickLabels: { fill: theme.colors.textSecondary, fontSize: 12, angle: -45 },
          }}
        />
        
        <VictoryBar
          data={purchaseData}
          style={{
            data: {
              fill: theme.colors.secondary,
              fillOpacity: 0.8,
            },
          }}
          labelComponent={<VictoryTooltip />}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
        />
        
        <VictoryBar
          data={usageData}
          style={{
            data: {
              fill: theme.colors.primary,
              fillOpacity: 0.8,
            },
          }}
          labelComponent={<VictoryTooltip />}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
        />
      </VictoryChart>
    </View>
  );
};

export default ComparisonChart;
