{"version": 3, "file": "host-component-names.js", "names": ["HOST_TEXT_NAMES", "HOST_TEXT_INPUT_NAMES", "HOST_IMAGE_NAMES", "HOST_SWITCH_NAMES", "HOST_SCROLL_VIEW_NAMES", "HOST_MODAL_NAMES", "isHostText", "element", "type", "includes", "isHostTextInput", "isHostImage", "isHostSwitch", "isHostScrollView", "isHostModal"], "sources": ["../../src/helpers/host-component-names.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport type { HostTestInstance } from './component-tree';\n\nconst HOST_TEXT_NAMES = ['Text', 'RCTText'];\nconst HOST_TEXT_INPUT_NAMES = ['TextInput'];\nconst HOST_IMAGE_NAMES = ['Image'];\nconst HOST_SWITCH_NAMES = ['RCTSwitch'];\nconst HOST_SCROLL_VIEW_NAMES = ['RCTScrollView'];\nconst HOST_MODAL_NAMES = ['Modal'];\n\n/**\n * Checks if the given element is a host Text element.\n * @param element The element to check.\n */\nexport function isHostText(element: ReactTestInstance): element is HostTestInstance {\n  return typeof element?.type === 'string' && HOST_TEXT_NAMES.includes(element.type);\n}\n\n/**\n * Checks if the given element is a host TextInput element.\n * @param element The element to check.\n */\nexport function isHostTextInput(element: ReactTestInstance): element is HostTestInstance {\n  return typeof element?.type === 'string' && HOST_TEXT_INPUT_NAMES.includes(element.type);\n}\n\n/**\n * Checks if the given element is a host Image element.\n * @param element The element to check.\n */\nexport function isHostImage(element: ReactTestInstance): element is HostTestInstance {\n  return typeof element?.type === 'string' && HOST_IMAGE_NAMES.includes(element.type);\n}\n\n/**\n * Checks if the given element is a host Switch element.\n * @param element The element to check.\n */\nexport function isHostSwitch(element: ReactTestInstance): element is HostTestInstance {\n  return typeof element?.type === 'string' && HOST_SWITCH_NAMES.includes(element.type);\n}\n\n/**\n * Checks if the given element is a host ScrollView element.\n * @param element The element to check.\n */\nexport function isHostScrollView(element: ReactTestInstance): element is HostTestInstance {\n  return typeof element?.type === 'string' && HOST_SCROLL_VIEW_NAMES.includes(element.type);\n}\n\n/**\n * Checks if the given element is a host Modal element.\n * @param element The element to check.\n */\nexport function isHostModal(element: ReactTestInstance): element is HostTestInstance {\n  return typeof element?.type === 'string' && HOST_MODAL_NAMES.includes(element.type);\n}\n"], "mappings": ";;;;;;;;;;;AAIA,MAAMA,eAAe,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC;AAC3C,MAAMC,qBAAqB,GAAG,CAAC,WAAW,CAAC;AAC3C,MAAMC,gBAAgB,GAAG,CAAC,OAAO,CAAC;AAClC,MAAMC,iBAAiB,GAAG,CAAC,WAAW,CAAC;AACvC,MAAMC,sBAAsB,GAAG,CAAC,eAAe,CAAC;AAChD,MAAMC,gBAAgB,GAAG,CAAC,OAAO,CAAC;;AAElC;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACC,OAA0B,EAA+B;EAClF,OAAO,OAAOA,OAAO,EAAEC,IAAI,KAAK,QAAQ,IAAIR,eAAe,CAACS,QAAQ,CAACF,OAAO,CAACC,IAAI,CAAC;AACpF;;AAEA;AACA;AACA;AACA;AACO,SAASE,eAAeA,CAACH,OAA0B,EAA+B;EACvF,OAAO,OAAOA,OAAO,EAAEC,IAAI,KAAK,QAAQ,IAAIP,qBAAqB,CAACQ,QAAQ,CAACF,OAAO,CAACC,IAAI,CAAC;AAC1F;;AAEA;AACA;AACA;AACA;AACO,SAASG,WAAWA,CAACJ,OAA0B,EAA+B;EACnF,OAAO,OAAOA,OAAO,EAAEC,IAAI,KAAK,QAAQ,IAAIN,gBAAgB,CAACO,QAAQ,CAACF,OAAO,CAACC,IAAI,CAAC;AACrF;;AAEA;AACA;AACA;AACA;AACO,SAASI,YAAYA,CAACL,OAA0B,EAA+B;EACpF,OAAO,OAAOA,OAAO,EAAEC,IAAI,KAAK,QAAQ,IAAIL,iBAAiB,CAACM,QAAQ,CAACF,OAAO,CAACC,IAAI,CAAC;AACtF;;AAEA;AACA;AACA;AACA;AACO,SAASK,gBAAgBA,CAACN,OAA0B,EAA+B;EACxF,OAAO,OAAOA,OAAO,EAAEC,IAAI,KAAK,QAAQ,IAAIJ,sBAAsB,CAACK,QAAQ,CAACF,OAAO,CAACC,IAAI,CAAC;AAC3F;;AAEA;AACA;AACA;AACA;AACO,SAASM,WAAWA,CAACP,OAA0B,EAA+B;EACnF,OAAO,OAAOA,OAAO,EAAEC,IAAI,KAAK,QAAQ,IAAIH,gBAAgB,CAACI,QAAQ,CAACF,OAAO,CAACC,IAAI,CAAC;AACrF", "ignoreList": []}