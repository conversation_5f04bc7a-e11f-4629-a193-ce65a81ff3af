{"version": 3, "file": "display-value.js", "names": ["_findAll", "require", "_hostComponentNames", "_textInput", "_matches", "_makeQueries", "matchDisplayValue", "node", "expectedValue", "options", "exact", "normalizer", "nodeValue", "getTextInputValue", "matches", "queryAllByDisplayValue", "instance", "queryAllByDisplayValueFn", "displayValue", "queryOptions", "findAll", "isHostTextInput", "getMultipleError", "String", "getMissingError", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy", "makeQueries", "bindByDisplayValueQueries", "getByDisplayValue", "getAllByDisplayValue", "queryByDisplayValue", "findByDisplayValue", "findAllByDisplayValue", "exports"], "sources": ["../../src/queries/display-value.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { findAll } from '../helpers/find-all';\nimport { isHostTextInput } from '../helpers/host-component-names';\nimport { getTextInputValue } from '../helpers/text-input';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport { matches } from '../matches';\nimport type {\n  FindAllByQuery,\n  FindByQuery,\n  GetAllByQuery,\n  GetByQuery,\n  QueryAllByQuery,\n  QueryByQuery,\n} from './make-queries';\nimport { makeQueries } from './make-queries';\nimport type { CommonQueryOptions } from './options';\n\ntype ByDisplayValueOptions = CommonQueryOptions & TextMatchOptions;\n\nconst matchDisplayValue = (\n  node: ReactTestInstance,\n  expectedValue: TextMatch,\n  options: TextMatchOptions = {},\n) => {\n  const { exact, normalizer } = options;\n  const nodeValue = getTextInputValue(node);\n  return matches(expectedValue, nodeValue, normalizer, exact);\n};\n\nconst queryAllByDisplayValue = (\n  instance: ReactTestInstance,\n): QueryAllByQuery<TextMatch, ByDisplayValueOptions> =>\n  function queryAllByDisplayValueFn(displayValue, queryOptions) {\n    return findAll(\n      instance,\n      (node) => isHostTextInput(node) && matchDisplayValue(node, displayValue, queryOptions),\n      queryOptions,\n    );\n  };\n\nconst getMultipleError = (displayValue: TextMatch) =>\n  `Found multiple elements with display value: ${String(displayValue)} `;\nconst getMissingError = (displayValue: TextMatch) =>\n  `Unable to find an element with displayValue: ${String(displayValue)}`;\n\nconst { getBy, getAllBy, queryBy, queryAllBy, findBy, findAllBy } = makeQueries(\n  queryAllByDisplayValue,\n  getMissingError,\n  getMultipleError,\n);\n\nexport type ByDisplayValueQueries = {\n  getByDisplayValue: GetByQuery<TextMatch, ByDisplayValueOptions>;\n  getAllByDisplayValue: GetAllByQuery<TextMatch, ByDisplayValueOptions>;\n  queryByDisplayValue: QueryByQuery<TextMatch, ByDisplayValueOptions>;\n  queryAllByDisplayValue: QueryAllByQuery<TextMatch, ByDisplayValueOptions>;\n  findByDisplayValue: FindByQuery<TextMatch, ByDisplayValueOptions>;\n  findAllByDisplayValue: FindAllByQuery<TextMatch, ByDisplayValueOptions>;\n};\n\nexport const bindByDisplayValueQueries = (instance: ReactTestInstance): ByDisplayValueQueries => ({\n  getByDisplayValue: getBy(instance),\n  getAllByDisplayValue: getAllBy(instance),\n  queryByDisplayValue: queryBy(instance),\n  queryAllByDisplayValue: queryAllBy(instance),\n  findByDisplayValue: findBy(instance),\n  findAllByDisplayValue: findAllBy(instance),\n});\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AASA,IAAAI,YAAA,GAAAJ,OAAA;AAKA,MAAMK,iBAAiB,GAAGA,CACxBC,IAAuB,EACvBC,aAAwB,EACxBC,OAAyB,GAAG,CAAC,CAAC,KAC3B;EACH,MAAM;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGF,OAAO;EACrC,MAAMG,SAAS,GAAG,IAAAC,4BAAiB,EAACN,IAAI,CAAC;EACzC,OAAO,IAAAO,gBAAO,EAACN,aAAa,EAAEI,SAAS,EAAED,UAAU,EAAED,KAAK,CAAC;AAC7D,CAAC;AAED,MAAMK,sBAAsB,GAC1BC,QAA2B,IAE3B,SAASC,wBAAwBA,CAACC,YAAY,EAAEC,YAAY,EAAE;EAC5D,OAAO,IAAAC,gBAAO,EACZJ,QAAQ,EACPT,IAAI,IAAK,IAAAc,mCAAe,EAACd,IAAI,CAAC,IAAID,iBAAiB,CAACC,IAAI,EAAEW,YAAY,EAAEC,YAAY,CAAC,EACtFA,YACF,CAAC;AACH,CAAC;AAEH,MAAMG,gBAAgB,GAAIJ,YAAuB,IAC/C,+CAA+CK,MAAM,CAACL,YAAY,CAAC,GAAG;AACxE,MAAMM,eAAe,GAAIN,YAAuB,IAC9C,gDAAgDK,MAAM,CAACL,YAAY,CAAC,EAAE;AAExE,MAAM;EAAEO,KAAK;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAU,CAAC,GAAG,IAAAC,wBAAW,EAC7EhB,sBAAsB,EACtBS,eAAe,EACfF,gBACF,CAAC;AAWM,MAAMU,yBAAyB,GAAIhB,QAA2B,KAA6B;EAChGiB,iBAAiB,EAAER,KAAK,CAACT,QAAQ,CAAC;EAClCkB,oBAAoB,EAAER,QAAQ,CAACV,QAAQ,CAAC;EACxCmB,mBAAmB,EAAER,OAAO,CAACX,QAAQ,CAAC;EACtCD,sBAAsB,EAAEa,UAAU,CAACZ,QAAQ,CAAC;EAC5CoB,kBAAkB,EAAEP,MAAM,CAACb,QAAQ,CAAC;EACpCqB,qBAAqB,EAAEP,SAAS,CAACd,QAAQ;AAC3C,CAAC,CAAC;AAACsB,OAAA,CAAAN,yBAAA,GAAAA,yBAAA", "ignoreList": []}