import React from 'react';
import { View, Dimensions } from 'react-native';
import {
  Victory<PERSON><PERSON>,
  VictoryContainer,
  VictoryTooltip,
  VictoryLegend,
} from 'victory-native';
import { useTheme } from '../../theme/ThemeContext';
import { ThemedText } from '../common/StyledComponents';

interface PieDataPoint {
  x: string;
  y: number;
  label?: string;
}

interface PieChartProps {
  data: PieDataPoint[];
  title?: string;
  size?: number;
  innerRadius?: number;
  colorScale?: string[];
}

const PieChart: React.FC<PieChartProps> = ({
  data,
  title = 'Usage Breakdown',
  size = 200,
  innerRadius = 0,
  colorScale,
}) => {
  const { theme } = useTheme();
  const screenWidth = Dimensions.get('window').width;
  
  const defaultColorScale = [
    theme.colors.primary,
    theme.colors.secondary,
    theme.colors.warning,
    theme.colors.success,
    theme.colors.info,
  ];
  
  const colors = colorScale || defaultColorScale;
  
  if (data.length === 0) {
    return (
      <View style={{ height: size + 100, justifyContent: 'center', alignItems: 'center' }}>
        <ThemedText color="textSecondary">No data available</ThemedText>
      </View>
    );
  }
  
  return (
    <View style={{ alignItems: 'center' }}>
      {title && (
        <ThemedText variant="h3" style={{ marginBottom: 16, textAlign: 'center' }}>
          {title}
        </ThemedText>
      )}
      
      <VictoryContainer width={screenWidth - 64} height={size + 80}>
        <VictoryPie
          data={data}
          width={screenWidth - 64}
          height={size}
          innerRadius={innerRadius}
          colorScale={colors}
          labelComponent={<VictoryTooltip />}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
          style={{
            labels: {
              fontSize: 12,
              fill: theme.colors.text,
              fontWeight: 'bold',
            },
          }}
        />
        
        <VictoryLegend
          x={20}
          y={size + 10}
          orientation="horizontal"
          gutter={20}
          style={{
            border: { stroke: theme.colors.border, fill: theme.colors.surface },
            title: { fontSize: 14, fill: theme.colors.text },
            labels: { fontSize: 12, fill: theme.colors.text },
          }}
          data={data.map((item, index) => ({
            name: item.x,
            symbol: { fill: colors[index % colors.length] },
          }))}
        />
      </VictoryContainer>
    </View>
  );
};

export default PieChart;
