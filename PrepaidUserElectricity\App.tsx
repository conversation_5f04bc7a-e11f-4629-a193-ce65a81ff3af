/**
 * Prepaid User Electricity App
 * Modern electricity usage tracking app
 *
 * @format
 */

import React, { useEffect, useRef } from 'react';
import { StatusBar } from 'react-native';
import { Provider } from 'react-redux';
import { NavigationContainerRef } from '@react-navigation/native';
import { store } from './src/store/store';
import { ThemeProvider } from './src/theme/ThemeContext';
import AppNavigator from './src/navigation/AppNavigator';
import DatabaseManager from './src/database/DatabaseManager';
import NotificationService from './src/services/NotificationService';
import NavigationService from './src/services/NavigationService';
import { DrawerParamList } from './src/navigation/AppNavigator';

const App = (): React.JSX.Element => {
  const navigationRef = useRef<NavigationContainerRef<DrawerParamList>>(null);

  useEffect(() => {
    // Initialize database and services when app starts
    const initializeApp = async () => {
      try {
        await DatabaseManager.initDatabase();
        console.log('Database initialized successfully');

        // Set navigation reference for notification service
        NavigationService.setNavigationRef(navigationRef);

        // Initialize notification service
        console.log('Notification service initialized');
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, []);

  return (
    <Provider store={store}>
      <ThemeProvider>
        <StatusBar
          barStyle="light-content"
          backgroundColor="#2196F3"
        />
        <AppNavigator navigationRef={navigationRef} />
      </ThemeProvider>
    </Provider>
  );
};

export default App;
