{"version": 3, "file": "to-be-visible.js", "names": ["_reactNative", "require", "_jestMatcherU<PERSON>s", "_redent", "_interopRequireDefault", "_accessibility", "_componentTree", "_formatElement", "_hostComponentNames", "_utils", "e", "__esModule", "default", "toBeVisible", "element", "isNot", "checkHostElement", "pass", "isElementVisible", "message", "is", "matcherHint", "redent", "formatElement", "join", "accessibilityCache", "cache", "WeakMap", "isHiddenFromAccessibility", "isHiddenForStyles", "isHostModal", "props", "visible", "hostParent", "getHostParent", "flatStyle", "StyleSheet", "flatten", "style", "display", "opacity"], "sources": ["../../src/matchers/to-be-visible.ts"], "sourcesContent": ["import { StyleSheet } from 'react-native';\nimport type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { isHiddenFromAccessibility } from '../helpers/accessibility';\nimport { getHostParent } from '../helpers/component-tree';\nimport { formatElement } from '../helpers/format-element';\nimport { isHostModal } from '../helpers/host-component-names';\nimport { checkHostElement } from './utils';\n\nexport function toBeVisible(this: jest.MatcherContext, element: ReactTestInstance) {\n  if (element !== null || !this.isNot) {\n    checkHostElement(element, toBeVisible, this);\n  }\n\n  return {\n    pass: isElementVisible(element),\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeVisible`, 'element', ''),\n        '',\n        `Received element ${is} visible:`,\n        redent(formatElement(element), 2),\n      ].join('\\n');\n    },\n  };\n}\n\nfunction isElementVisible(\n  element: ReactTestInstance,\n  accessibilityCache?: WeakMap<ReactTestInstance, boolean>,\n): boolean {\n  // Use cache to speed up repeated searches by `isHiddenFromAccessibility`.\n  const cache = accessibilityCache ?? new WeakMap<ReactTestInstance, boolean>();\n  if (isHiddenFromAccessibility(element, { cache })) {\n    return false;\n  }\n\n  if (isHiddenForStyles(element)) {\n    return false;\n  }\n\n  // Note: this seems to be a bug in React Native.\n  // PR with fix: https://github.com/facebook/react-native/pull/39157\n  if (isHostModal(element) && element.props.visible === false) {\n    return false;\n  }\n\n  const hostParent = getHostParent(element);\n  if (hostParent === null) {\n    return true;\n  }\n\n  return isElementVisible(hostParent, cache);\n}\n\nfunction isHiddenForStyles(element: ReactTestInstance) {\n  const flatStyle = StyleSheet.flatten(element.props.style);\n  return flatStyle?.display === 'none' || flatStyle?.opacity === 0;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,cAAA,GAAAN,OAAA;AACA,IAAAO,mBAAA,GAAAP,OAAA;AACA,IAAAQ,MAAA,GAAAR,OAAA;AAA2C,SAAAG,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,WAAWA,CAA4BC,OAA0B,EAAE;EACjF,IAAIA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;IACnC,IAAAC,uBAAgB,EAACF,OAAO,EAAED,WAAW,EAAE,IAAI,CAAC;EAC9C;EAEA,OAAO;IACLI,IAAI,EAAEC,gBAAgB,CAACJ,OAAO,CAAC;IAC/BK,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACL,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAM,6BAAW,EAAC,GAAG,IAAI,CAACN,KAAK,GAAG,MAAM,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC,EACrE,EAAE,EACF,oBAAoBK,EAAE,WAAW,EACjC,IAAAE,eAAM,EAAC,IAAAC,4BAAa,EAACT,OAAO,CAAC,EAAE,CAAC,CAAC,CAClC,CAACU,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEA,SAASN,gBAAgBA,CACvBJ,OAA0B,EAC1BW,kBAAwD,EAC/C;EACT;EACA,MAAMC,KAAK,GAAGD,kBAAkB,IAAI,IAAIE,OAAO,CAA6B,CAAC;EAC7E,IAAI,IAAAC,wCAAyB,EAACd,OAAO,EAAE;IAAEY;EAAM,CAAC,CAAC,EAAE;IACjD,OAAO,KAAK;EACd;EAEA,IAAIG,iBAAiB,CAACf,OAAO,CAAC,EAAE;IAC9B,OAAO,KAAK;EACd;;EAEA;EACA;EACA,IAAI,IAAAgB,+BAAW,EAAChB,OAAO,CAAC,IAAIA,OAAO,CAACiB,KAAK,CAACC,OAAO,KAAK,KAAK,EAAE;IAC3D,OAAO,KAAK;EACd;EAEA,MAAMC,UAAU,GAAG,IAAAC,4BAAa,EAACpB,OAAO,CAAC;EACzC,IAAImB,UAAU,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,OAAOf,gBAAgB,CAACe,UAAU,EAAEP,KAAK,CAAC;AAC5C;AAEA,SAASG,iBAAiBA,CAACf,OAA0B,EAAE;EACrD,MAAMqB,SAAS,GAAGC,uBAAU,CAACC,OAAO,CAACvB,OAAO,CAACiB,KAAK,CAACO,KAAK,CAAC;EACzD,OAAOH,SAAS,EAAEI,OAAO,KAAK,MAAM,IAAIJ,SAAS,EAAEK,OAAO,KAAK,CAAC;AAClE", "ignoreList": []}