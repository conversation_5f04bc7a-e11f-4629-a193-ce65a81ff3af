export interface ThemeColors {
  primary: string;
  primaryDark: string;
  secondary: string;
  background: string;
  surface: string;
  card: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  gradient: string[];
}

export interface Theme {
  name: string;
  colors: ThemeColors;
}

export const themes: Record<string, Theme> = {
  default: {
    name: 'Default Blue',
    colors: {
      primary: '#2196F3',
      primaryDark: '#1976D2',
      secondary: '#03DAC6',
      background: '#F5F5F5',
      surface: '#FFFFFF',
      card: '#FFFFFF',
      text: '#212121',
      textSecondary: '#757575',
      border: '#E0E0E0',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      gradient: ['#2196F3', '#1976D2'],
    },
  },
  dark: {
    name: 'Dark Mode',
    colors: {
      primary: '#BB86FC',
      primaryDark: '#3700B3',
      secondary: '#03DAC6',
      background: '#121212',
      surface: '#1E1E1E',
      card: '#2D2D2D',
      text: '#FFFFFF',
      textSecondary: '#B3B3B3',
      border: '#333333',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#CF6679',
      info: '#BB86FC',
      gradient: ['#BB86FC', '#3700B3'],
    },
  },
  blue: {
    name: 'Ocean Blue',
    colors: {
      primary: '#0277BD',
      primaryDark: '#01579B',
      secondary: '#00BCD4',
      background: '#E3F2FD',
      surface: '#FFFFFF',
      card: '#FFFFFF',
      text: '#0D47A1',
      textSecondary: '#1976D2',
      border: '#BBDEFB',
      success: '#00C853',
      warning: '#FF8F00',
      error: '#D32F2F',
      info: '#0277BD',
      gradient: ['#0277BD', '#01579B'],
    },
  },
  green: {
    name: 'Nature Green',
    colors: {
      primary: '#388E3C',
      primaryDark: '#1B5E20',
      secondary: '#4CAF50',
      background: '#E8F5E8',
      surface: '#FFFFFF',
      card: '#FFFFFF',
      text: '#1B5E20',
      textSecondary: '#2E7D32',
      border: '#C8E6C9',
      success: '#4CAF50',
      warning: '#FF8F00',
      error: '#D32F2F',
      info: '#388E3C',
      gradient: ['#388E3C', '#1B5E20'],
    },
  },
  purple: {
    name: 'Royal Purple',
    colors: {
      primary: '#7B1FA2',
      primaryDark: '#4A148C',
      secondary: '#E1BEE7',
      background: '#F3E5F5',
      surface: '#FFFFFF',
      card: '#FFFFFF',
      text: '#4A148C',
      textSecondary: '#6A1B9A',
      border: '#E1BEE7',
      success: '#4CAF50',
      warning: '#FF8F00',
      error: '#D32F2F',
      info: '#7B1FA2',
      gradient: ['#7B1FA2', '#4A148C'],
    },
  },
};

export interface FontConfig {
  name: string;
  fontFamily: string;
  sizes: {
    small: number;
    medium: number;
    large: number;
    xlarge: number;
    xxlarge: number;
  };
  weights: {
    light: '300' as const;
    regular: '400' as const;
    medium: '500' as const;
    bold: '700' as const;
  };
}

export const fonts: Record<string, FontConfig> = {
  default: {
    name: 'System Default',
    fontFamily: 'System',
    sizes: {
      small: 12,
      medium: 14,
      large: 16,
      xlarge: 20,
      xxlarge: 24,
    },
    weights: {
      light: '300' as const,
      regular: '400' as const,
      medium: '500' as const,
      bold: '700' as const,
    },
  },
  roboto: {
    name: 'Roboto',
    fontFamily: 'Roboto',
    sizes: {
      small: 12,
      medium: 14,
      large: 16,
      xlarge: 20,
      xxlarge: 24,
    },
    weights: {
      light: '300' as const,
      regular: '400' as const,
      medium: '500' as const,
      bold: '700' as const,
    },
  },
  opensans: {
    name: 'Open Sans',
    fontFamily: 'OpenSans',
    sizes: {
      small: 12,
      medium: 14,
      large: 16,
      xlarge: 20,
      xxlarge: 24,
    },
    weights: {
      light: '300' as const,
      regular: '400' as const,
      medium: '600' as const,
      bold: '700' as const,
    },
  },
  lato: {
    name: 'Lato',
    fontFamily: 'Lato',
    sizes: {
      small: 12,
      medium: 14,
      large: 16,
      xlarge: 20,
      xxlarge: 24,
    },
    weights: {
      light: '300' as const,
      regular: '400' as const,
      medium: '500' as const,
      bold: '700' as const,
    },
  },
  montserrat: {
    name: 'Montserrat',
    fontFamily: 'Montserrat',
    sizes: {
      small: 12,
      medium: 14,
      large: 16,
      xlarge: 20,
      xxlarge: 24,
    },
    weights: {
      light: '300' as const,
      regular: '400' as const,
      medium: '500' as const,
      bold: '700' as const,
    },
  },
};

export const getTheme = (themeName: string): Theme => {
  return themes[themeName] || themes.default;
};

export const getFont = (fontName: string): FontConfig => {
  return fonts[fontName] || fonts.default;
};
