import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { addPurchase, loadPurchases } from '../store/slices/purchasesSlice';
import { loadSettings } from '../store/slices/settingsSlice';
import {
  Container,
  Card,
  ThemedText,
  ThemedTextInput,
  GradientButton,
} from '../components/common/StyledComponents';
import { useTheme } from '../theme/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialIcons';

const PurchasesScreen = () => {
  const dispatch = useAppDispatch();
  const { theme, font } = useTheme();

  const [currencyAmount, setCurrencyAmount] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Redux state
  const settings = useAppSelector((state) => state.settings.settings);
  const purchases = useAppSelector((state) => state.purchases.purchases);
  const { weeklyTotal, monthlyTotal } = useAppSelector((state) => state.purchases);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        dispatch(loadSettings()),
        dispatch(loadPurchases()),
      ]);
    } catch (error) {
      console.error('Failed to load purchases data:', error);
    }
  };

  // Live calculations
  const currencyValue = parseFloat(currencyAmount) || 0;
  const unitCost = settings?.unitCost || 0.15;
  const calculatedUnits = currencyValue / unitCost;
  const currencySymbol = settings?.currencyType === 'Custom'
    ? settings?.customCurrencyName || 'CUR'
    : settings?.currencyType || 'USD';
  const unitName = settings?.unitType === 'Custom'
    ? settings?.customUnitName || 'Units'
    : settings?.unitType || 'Units';

  const handleAddPurchase = async () => {
    if (!currencyAmount || currencyValue <= 0) {
      Alert.alert('Invalid Input', 'Please enter a valid currency amount.');
      return;
    }

    if (!settings) {
      Alert.alert('Error', 'Settings not loaded. Please try again.');
      return;
    }

    setIsLoading(true);

    try {
      const purchase = {
        currency: currencyValue,
        units: calculatedUnits,
        unitCost: unitCost,
        timestamp: new Date().toISOString(),
        currencyType: currencySymbol,
      };

      await dispatch(addPurchase(purchase));

      // Clear form
      setCurrencyAmount('');

      Alert.alert(
        'Purchase Added',
        `Successfully added ${calculatedUnits.toFixed(2)} ${unitName} for ${currencySymbol} ${currencyValue.toFixed(2)}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to add purchase. Please try again.');
      console.error('Failed to add purchase:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!settings) {
    return (
      <Container>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ThemedText variant="h2">Loading...</ThemedText>
        </View>
      </Container>
    );
  }

  return (
    <Container>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Purchase Input Section */}
          <Card>
            <ThemedText variant="h2" style={{ marginBottom: 20, textAlign: 'center' }}>
              Add New Purchase
            </ThemedText>

            <ThemedTextInput
              label={`Currency Amount (${currencySymbol})`}
              value={currencyAmount}
              onChangeText={setCurrencyAmount}
              keyboardType="numeric"
              placeholder={`Enter amount in ${currencySymbol}`}
              icon="attach-money"
            />

            {/* Live Preview */}
            <Card style={{ backgroundColor: theme.colors.background, marginTop: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <Icon name="preview" size={20} color={theme.colors.primary} />
                <ThemedText variant="h3" style={{ marginLeft: 8 }}>
                  Live Preview
                </ThemedText>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                <ThemedText color="textSecondary">Currency Amount:</ThemedText>
                <ThemedText variant="h3" color="primary">
                  {currencySymbol} {currencyValue.toFixed(2)}
                </ThemedText>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                <ThemedText color="textSecondary">Unit Cost:</ThemedText>
                <ThemedText>
                  {currencySymbol} {unitCost.toFixed(3)} per {unitName}
                </ThemedText>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                <ThemedText color="textSecondary">Units to Receive:</ThemedText>
                <ThemedText variant="h3" color="secondary">
                  {calculatedUnits.toFixed(2)} {unitName}
                </ThemedText>
              </View>

              <View style={{ height: 1, backgroundColor: theme.colors.border, marginVertical: 8 }} />

              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <ThemedText variant="h3">Total Value:</ThemedText>
                <ThemedText variant="h3" color="primary">
                  {calculatedUnits.toFixed(2)} {unitName} = {currencySymbol} {currencyValue.toFixed(2)}
                </ThemedText>
              </View>
            </Card>

            <GradientButton
              title={isLoading ? "Adding..." : "Add Purchase"}
              icon="add-shopping-cart"
              onPress={handleAddPurchase}
              disabled={isLoading || !currencyAmount || currencyValue <= 0}
              style={{ marginTop: 20 }}
            />
          </Card>

          {/* Purchase Summary */}
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Purchase Summary
            </ThemedText>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Icon name="date-range" size={24} color={theme.colors.secondary} />
                <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                  Weekly Total
                </ThemedText>
                <ThemedText variant="h3" color="secondary">
                  {currencySymbol} {weeklyTotal.toFixed(2)}
                </ThemedText>
              </View>

              <View style={{ width: 1, backgroundColor: theme.colors.border, marginHorizontal: 16 }} />

              <View style={{ flex: 1, alignItems: 'center' }}>
                <Icon name="calendar-today" size={24} color={theme.colors.secondary} />
                <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                  Monthly Total
                </ThemedText>
                <ThemedText variant="h3" color="secondary">
                  {currencySymbol} {monthlyTotal.toFixed(2)}
                </ThemedText>
              </View>
            </View>
          </Card>

          {/* Recent Purchases */}
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Recent Purchases
            </ThemedText>

            {purchases.length === 0 ? (
              <View style={{ alignItems: 'center', paddingVertical: 20 }}>
                <Icon name="shopping-cart" size={48} color={theme.colors.textSecondary} />
                <ThemedText color="textSecondary" style={{ marginTop: 8 }}>
                  No purchases yet
                </ThemedText>
                <ThemedText variant="caption" color="textSecondary">
                  Add your first purchase above
                </ThemedText>
              </View>
            ) : (
              purchases.slice(0, 5).map((purchase, index) => (
                <View
                  key={purchase.id || index}
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingVertical: 12,
                    borderBottomWidth: index < Math.min(purchases.length, 5) - 1 ? 1 : 0,
                    borderBottomColor: theme.colors.border,
                  }}
                >
                  <View style={{ flex: 1 }}>
                    <ThemedText variant="h3">
                      {currencySymbol} {purchase.currency.toFixed(2)}
                    </ThemedText>
                    <ThemedText variant="caption" color="textSecondary">
                      {formatDate(purchase.timestamp)}
                    </ThemedText>
                  </View>

                  <View style={{ alignItems: 'flex-end' }}>
                    <ThemedText color="secondary">
                      {purchase.units.toFixed(2)} {unitName}
                    </ThemedText>
                    <ThemedText variant="caption" color="textSecondary">
                      @ {currencySymbol} {purchase.unitCost.toFixed(3)}
                    </ThemedText>
                  </View>
                </View>
              ))
            )}

            {purchases.length > 5 && (
              <ThemedText
                variant="caption"
                color="primary"
                style={{ textAlign: 'center', marginTop: 12 }}
              >
                View all in History
              </ThemedText>
            )}
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </Container>
  );
};

export default PurchasesScreen;
