{"version": 3, "file": "unsafe-type.js", "names": ["_errors", "require", "UNSAFE_getByType", "instance", "getByTypeFn", "type", "findByType", "error", "ErrorWithStack", "prepareErrorMessage", "UNSAFE_getAllByType", "getAllByTypeFn", "results", "findAllByType", "length", "UNSAFE_queryByType", "queryByTypeFn", "createQueryByError", "UNSAFE_queryAllByType", "bindUnsafeByTypeQueries", "exports"], "sources": ["../../src/queries/unsafe-type.ts"], "sourcesContent": ["import type * as React from 'react';\nimport type { ReactTestInstance } from 'react-test-renderer';\n\nimport { ErrorWithStack, prepareErrorMessage } from '../helpers/errors';\nimport { createQueryByError } from '../helpers/errors';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type UnsafeComponentType = React.ComponentType<any>;\n\nconst UNSAFE_getByType = (\n  instance: ReactTestInstance,\n): ((type: UnsafeComponentType) => ReactTestInstance) =>\n  function getByTypeFn(type: UnsafeComponentType) {\n    try {\n      return instance.findByType(type);\n    } catch (error) {\n      throw new ErrorWithStack(prepareErrorMessage(error), getByTypeFn);\n    }\n  };\n\nconst UNSAFE_getAllByType = (\n  instance: ReactTestInstance,\n): ((type: UnsafeComponentType) => Array<ReactTestInstance>) =>\n  function getAllByTypeFn(type: UnsafeComponentType) {\n    const results = instance.findAllByType(type);\n    if (results.length === 0) {\n      throw new ErrorWithStack('No instances found', getAllByTypeFn);\n    }\n    return results;\n  };\n\nconst UNSAFE_queryByType = (\n  instance: ReactTestInstance,\n): ((type: UnsafeComponentType) => ReactTestInstance | null) =>\n  function queryByTypeFn(type: UnsafeComponentType) {\n    try {\n      return UNSAFE_getByType(instance)(type);\n    } catch (error) {\n      return createQueryByError(error, queryByTypeFn);\n    }\n  };\n\nconst UNSAFE_queryAllByType =\n  (instance: ReactTestInstance): ((type: UnsafeComponentType) => Array<ReactTestInstance>) =>\n  (type: UnsafeComponentType) => {\n    try {\n      return UNSAFE_getAllByType(instance)(type);\n    } catch {\n      return [];\n    }\n  };\n\n// Unsafe aliases\nexport type UnsafeByTypeQueries = {\n  UNSAFE_getByType: <P>(type: React.ComponentType<P>) => ReactTestInstance;\n  UNSAFE_getAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;\n  UNSAFE_queryByType: <P>(type: React.ComponentType<P>) => ReactTestInstance | null;\n  UNSAFE_queryAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;\n};\n\n// TODO: migrate to makeQueries pattern\nexport const bindUnsafeByTypeQueries = (instance: ReactTestInstance): UnsafeByTypeQueries => ({\n  UNSAFE_getByType: UNSAFE_getByType(instance),\n  UNSAFE_getAllByType: UNSAFE_getAllByType(instance),\n  UNSAFE_queryByType: UNSAFE_queryByType(instance),\n  UNSAFE_queryAllByType: UNSAFE_queryAllByType(instance),\n});\n"], "mappings": ";;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAGA;;AAGA,MAAMC,gBAAgB,GACpBC,QAA2B,IAE3B,SAASC,WAAWA,CAACC,IAAyB,EAAE;EAC9C,IAAI;IACF,OAAOF,QAAQ,CAACG,UAAU,CAACD,IAAI,CAAC;EAClC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAM,IAAIC,sBAAc,CAAC,IAAAC,2BAAmB,EAACF,KAAK,CAAC,EAAEH,WAAW,CAAC;EACnE;AACF,CAAC;AAEH,MAAMM,mBAAmB,GACvBP,QAA2B,IAE3B,SAASQ,cAAcA,CAACN,IAAyB,EAAE;EACjD,MAAMO,OAAO,GAAGT,QAAQ,CAACU,aAAa,CAACR,IAAI,CAAC;EAC5C,IAAIO,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,MAAM,IAAIN,sBAAc,CAAC,oBAAoB,EAAEG,cAAc,CAAC;EAChE;EACA,OAAOC,OAAO;AAChB,CAAC;AAEH,MAAMG,kBAAkB,GACtBZ,QAA2B,IAE3B,SAASa,aAAaA,CAACX,IAAyB,EAAE;EAChD,IAAI;IACF,OAAOH,gBAAgB,CAACC,QAAQ,CAAC,CAACE,IAAI,CAAC;EACzC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAO,IAAAU,0BAAkB,EAACV,KAAK,EAAES,aAAa,CAAC;EACjD;AACF,CAAC;AAEH,MAAME,qBAAqB,GACxBf,QAA2B,IAC3BE,IAAyB,IAAK;EAC7B,IAAI;IACF,OAAOK,mBAAmB,CAACP,QAAQ,CAAC,CAACE,IAAI,CAAC;EAC5C,CAAC,CAAC,MAAM;IACN,OAAO,EAAE;EACX;AACF,CAAC;;AAEH;;AAQA;AACO,MAAMc,uBAAuB,GAAIhB,QAA2B,KAA2B;EAC5FD,gBAAgB,EAAEA,gBAAgB,CAACC,QAAQ,CAAC;EAC5CO,mBAAmB,EAAEA,mBAAmB,CAACP,QAAQ,CAAC;EAClDY,kBAAkB,EAAEA,kBAAkB,CAACZ,QAAQ,CAAC;EAChDe,qBAAqB,EAAEA,qBAAqB,CAACf,QAAQ;AACvD,CAAC,CAAC;AAACiB,OAAA,CAAAD,uBAAA,GAAAA,uBAAA", "ignoreList": []}