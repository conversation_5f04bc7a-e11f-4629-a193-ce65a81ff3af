import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import DatabaseManager, { UsageRecord } from '../../database/DatabaseManager';

export interface UsageState {
  usageRecords: UsageRecord[];
  latestRecord: UsageRecord | null;
  isLoading: boolean;
  error: string | null;
  weeklyUsage: number;
  monthlyUsage: number;
  weeklyUsageCost: number;
  monthlyUsageCost: number;
  usageSinceLastRecording: number;
}

const initialState: UsageState = {
  usageRecords: [],
  latestRecord: null,
  isLoading: false,
  error: null,
  weeklyUsage: 0,
  monthlyUsage: 0,
  weeklyUsageCost: 0,
  monthlyUsageCost: 0,
  usageSinceLastRecording: 0,
};

// Async thunks
export const loadUsageRecords = createAsyncThunk(
  'usage/loadUsageRecords',
  async (_, { rejectWithValue }) => {
    try {
      const usageRecords = await DatabaseManager.getUsageRecords();
      const latestRecord = await DatabaseManager.getLatestUsageRecord();
      return { usageRecords, latestRecord };
    } catch (error) {
      return rejectWithValue('Failed to load usage records');
    }
  }
);

export const addUsageRecord = createAsyncThunk(
  'usage/addUsageRecord',
  async (usageRecord: Omit<UsageRecord, 'id'>, { rejectWithValue }) => {
    try {
      const recordId = await DatabaseManager.addUsageRecord(usageRecord);
      const usageRecords = await DatabaseManager.getUsageRecords();
      const latestRecord = await DatabaseManager.getLatestUsageRecord();
      return { usageRecords, latestRecord, recordId };
    } catch (error) {
      return rejectWithValue('Failed to add usage record');
    }
  }
);

export const loadUsageTotals = createAsyncThunk(
  'usage/loadTotals',
  async (_, { rejectWithValue }) => {
    try {
      const weeklyTotals = await DatabaseManager.getWeeklyTotals();
      const monthlyTotals = await DatabaseManager.getMonthlyTotals();
      return {
        weekly: weeklyTotals.usage,
        monthly: monthlyTotals.usage,
        weeklyCost: weeklyTotals.cost,
        monthlyCost: monthlyTotals.cost,
      };
    } catch (error) {
      return rejectWithValue('Failed to load usage totals');
    }
  }
);

export const calculateUsageSinceLastRecording = createAsyncThunk(
  'usage/calculateUsageSinceLastRecording',
  async (currentUnits: number, { rejectWithValue }) => {
    try {
      const latestRecord = await DatabaseManager.getLatestUsageRecord();
      if (latestRecord) {
        const usageDifference = latestRecord.currentUnits - currentUnits;
        return Math.max(0, usageDifference); // Ensure non-negative
      }
      return 0;
    } catch (error) {
      return rejectWithValue('Failed to calculate usage since last recording');
    }
  }
);

const usageSlice = createSlice({
  name: 'usage',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUsageRecords: (state, action: PayloadAction<UsageRecord[]>) => {
      state.usageRecords = action.payload;
    },
    setLatestRecord: (state, action: PayloadAction<UsageRecord | null>) => {
      state.latestRecord = action.payload;
    },
    updateUsageTotals: (state, action: PayloadAction<{
      weekly: number;
      monthly: number;
      weeklyCost: number;
      monthlyCost: number;
    }>) => {
      state.weeklyUsage = action.payload.weekly;
      state.monthlyUsage = action.payload.monthly;
      state.weeklyUsageCost = action.payload.weeklyCost;
      state.monthlyUsageCost = action.payload.monthlyCost;
    },
    setUsageSinceLastRecording: (state, action: PayloadAction<number>) => {
      state.usageSinceLastRecording = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Load usage records
      .addCase(loadUsageRecords.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadUsageRecords.fulfilled, (state, action) => {
        state.isLoading = false;
        state.usageRecords = action.payload.usageRecords;
        state.latestRecord = action.payload.latestRecord;
      })
      .addCase(loadUsageRecords.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Add usage record
      .addCase(addUsageRecord.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addUsageRecord.fulfilled, (state, action) => {
        state.isLoading = false;
        state.usageRecords = action.payload.usageRecords;
        state.latestRecord = action.payload.latestRecord;
      })
      .addCase(addUsageRecord.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Load totals
      .addCase(loadUsageTotals.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadUsageTotals.fulfilled, (state, action) => {
        state.isLoading = false;
        state.weeklyUsage = action.payload.weekly;
        state.monthlyUsage = action.payload.monthly;
        state.weeklyUsageCost = action.payload.weeklyCost;
        state.monthlyUsageCost = action.payload.monthlyCost;
      })
      .addCase(loadUsageTotals.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Calculate usage since last recording
      .addCase(calculateUsageSinceLastRecording.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(calculateUsageSinceLastRecording.fulfilled, (state, action) => {
        state.isLoading = false;
        state.usageSinceLastRecording = action.payload;
      })
      .addCase(calculateUsageSinceLastRecording.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setUsageRecords,
  setLatestRecord,
  updateUsageTotals,
  setUsageSinceLastRecording,
} = usageSlice.actions;

export default usageSlice.reducer;
