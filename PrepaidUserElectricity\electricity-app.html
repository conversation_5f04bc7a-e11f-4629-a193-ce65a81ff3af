<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prepaid User Electricity</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 10px;
            overflow-x: hidden;
        }
        
        .container { max-width: 400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 48px; margin-bottom: 10px; animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.1); } }
        .title { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .subtitle { font-size: 16px; opacity: 0.8; }
        
        .dial-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .dial {
            width: 150px;
            height: 150px;
            border: 8px solid rgba(255, 255, 255, 0.3);
            border-top: 8px solid #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            animation: spin 3s linear infinite;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        
        .units {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4CAF50;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .units-label { font-size: 18px; opacity: 0.8; }
        
        .warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
            animation: blink 1s infinite;
        }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.7; } }
        
        .controls { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 30px 0; }
        .control-group {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        .control-group h3 { font-size: 16px; margin-bottom: 15px; text-align: center; }
        .input-group { margin-bottom: 15px; }
        .input-group label { display: block; font-size: 12px; margin-bottom: 5px; opacity: 0.8; }
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            border: 2px solid transparent;
            transition: border-color 0.3s;
        }
        .input-group input:focus, .input-group select:focus { outline: none; border-color: #4CAF50; }
        .input-group input::placeholder { color: rgba(255, 255, 255, 0.6); }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4); }
        .btn:active { transform: translateY(0); }
        
        .history {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        .history h3 { font-size: 18px; margin-bottom: 15px; text-align: center; }
        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn { from { opacity: 0; transform: translateX(-20px); } to { opacity: 1; transform: translateX(0); } }
        .history-item:last-child { border-bottom: none; }
        .history-type { font-size: 20px; margin-right: 10px; }
        .history-details { flex: 1; }
        .history-amount { font-weight: bold; color: #4CAF50; }
        
        .summary { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .summary-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .summary-item:hover { transform: translateY(-5px); }
        .summary-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .summary-label { font-size: 12px; opacity: 0.8; }
        
        .settings {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        .settings h3 { font-size: 18px; margin-bottom: 15px; text-align: center; }
        .settings-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
        
        .footer { text-align: center; margin-top: 30px; opacity: 0.6; font-size: 12px; }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s;
            z-index: 1000;
        }
        .notification.show { transform: translateX(0); }
        
        .tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: background 0.3s;
            border: none;
            background: transparent;
            color: white;
            font-size: 14px;
        }
        .tab.active { background: rgba(255, 255, 255, 0.2); }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">⚡</div>
            <div class="title">Prepaid User Electricity</div>
            <div class="subtitle">Smart Electricity Management</div>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('dashboard')">🏠 Dashboard</button>
            <button class="tab" onclick="showTab('history')">📋 History</button>
            <button class="tab" onclick="showTab('settings')">⚙️ Settings</button>
        </div>
        
        <div id="dashboard" class="tab-content active">
            <div class="dial-container">
                <div class="dial"></div>
                <div class="units" id="currentUnits">75.0</div>
                <div class="units-label">Units Remaining</div>
            </div>
            
            <div id="lowUnitsWarning" class="warning" style="display: none;">
                ⚠️ Low units remaining! Consider purchasing more electricity.
            </div>
            
            <div class="summary">
                <div class="summary-item">
                    <div class="summary-value" id="weeklyUsage">25.0</div>
                    <div class="summary-label">Weekly Usage</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="monthlyCost">$15.00</div>
                    <div class="summary-label">Monthly Cost</div>
                </div>
            </div>
            
            <div class="controls">
                <div class="control-group">
                    <h3>💰 Add Purchase</h3>
                    <div class="input-group">
                        <label>Currency</label>
                        <select id="currency">
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="ZAR">ZAR (R)</option>
                            <option value="NGN">NGN (₦)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>Amount</label>
                        <input type="number" id="purchaseAmount" placeholder="50.00" step="0.01">
                    </div>
                    <div class="input-group">
                        <label>Units Received</label>
                        <input type="number" id="purchaseUnits" placeholder="333.33" step="0.01" readonly>
                    </div>
                    <button class="btn" onclick="addPurchase()">Add Purchase</button>
                </div>
                
                <div class="control-group">
                    <h3>📊 Record Usage</h3>
                    <div class="input-group">
                        <label>Previous Reading</label>
                        <input type="number" id="previousReading" placeholder="100.0" step="0.1" readonly>
                    </div>
                    <div class="input-group">
                        <label>Current Reading</label>
                        <input type="number" id="currentReading" placeholder="75.0" step="0.1">
                    </div>
                    <div class="input-group">
                        <label>Usage Amount</label>
                        <input type="number" id="usageAmount" placeholder="25.0" step="0.1" readonly>
                    </div>
                    <button class="btn" onclick="recordUsage()">Record Usage</button>
                </div>
            </div>
        </div>
        
        <div id="history" class="tab-content">
            <div class="history">
                <h3>📋 Transaction History</h3>
                <div class="controls" style="margin-bottom: 20px;">
                    <div class="control-group">
                        <div class="input-group">
                            <label>Filter by Type</label>
                            <select id="historyFilter" onchange="filterHistory()">
                                <option value="all">All Transactions</option>
                                <option value="purchase">Purchases Only</option>
                                <option value="usage">Usage Only</option>
                            </select>
                        </div>
                    </div>
                    <div class="control-group">
                        <div class="input-group">
                            <label>Time Period</label>
                            <select id="timeFilter" onchange="filterHistory()">
                                <option value="all">All Time</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div id="historyList"></div>
            </div>
        </div>
        
        <div id="settings" class="tab-content">
            <div class="settings">
                <h3>⚙️ App Settings</h3>
                <div class="settings-grid">
                    <div class="input-group">
                        <label>Unit Cost ($)</label>
                        <input type="number" id="unitCost" value="0.15" step="0.01" onchange="updateUnitCost()">
                    </div>
                    <div class="input-group">
                        <label>Low Units Threshold</label>
                        <input type="number" id="lowUnitsThreshold" value="20" step="1" onchange="updateThreshold()">
                    </div>
                    <div class="input-group">
                        <label>Default Currency</label>
                        <select id="defaultCurrency" onchange="updateDefaultCurrency()">
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="ZAR">ZAR (R)</option>
                            <option value="NGN">NGN (₦)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>Unit Type</label>
                        <select id="unitType" onchange="updateUnitType()">
                            <option value="Units">Units</option>
                            <option value="KWh">KWh</option>
                            <option value="Custom">Custom</option>
                        </select>
                    </div>
                </div>
                <button class="btn" onclick="resetData()" style="margin-top: 20px; background: linear-gradient(45deg, #f44336, #d32f2f);">Reset All Data</button>
            </div>
        </div>
        
        <div class="footer">
            <p>Prepaid User Electricity v1.0.0</p>
            <p>Built with ❤️ for efficient electricity management</p>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script>
        // App state
        let currentUnits = 75.0;
        let previousReading = 100.0;
        let unitCost = 0.15;
        let lowUnitsThreshold = 20;
        let defaultCurrency = 'USD';
        let unitType = 'Units';
        let history = [
            { type: 'purchase', amount: 50.00, units: 333.3, currency: 'USD', time: new Date(Date.now() - 86400000).toLocaleString() },
            { type: 'usage', units: 25.0, time: new Date(Date.now() - 172800000).toLocaleString() }
        ];

        // Currency symbols
        const currencySymbols = {
            'USD': '$', 'EUR': '€', 'GBP': '£', 'ZAR': 'R', 'NGN': '₦'
        };

        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            setTimeout(() => notification.classList.remove('show'), 3000);
        }

        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            if (tabName === 'history') filterHistory();
        }

        function updateDisplay() {
            document.getElementById('currentUnits').textContent = currentUnits.toFixed(1);
            document.getElementById('previousReading').value = previousReading.toFixed(1);

            const warning = document.getElementById('lowUnitsWarning');
            warning.style.display = currentUnits <= lowUnitsThreshold ? 'block' : 'none';

            const now = new Date();
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

            let weeklyUsage = history.filter(h => h.type === 'usage' && new Date(h.time) >= weekAgo)
                                   .reduce((sum, h) => sum + h.units, 0);
            let monthlyCost = history.filter(h => h.type === 'purchase' && new Date(h.time) >= monthAgo)
                                   .reduce((sum, h) => sum + h.amount, 0);

            document.getElementById('weeklyUsage').textContent = weeklyUsage.toFixed(1);
            document.getElementById('monthlyCost').textContent = currencySymbols[defaultCurrency] + monthlyCost.toFixed(2);
        }

        function updateHistory() {
            const historyList = document.getElementById('historyList');
            historyList.innerHTML = '';

            let filteredHistory = [...history];
            const typeFilter = document.getElementById('historyFilter')?.value || 'all';
            const timeFilter = document.getElementById('timeFilter')?.value || 'all';

            if (typeFilter !== 'all') {
                filteredHistory = filteredHistory.filter(item => item.type === typeFilter);
            }

            if (timeFilter !== 'all') {
                const now = new Date();
                const filterDate = timeFilter === 'week'
                    ? new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
                    : new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                filteredHistory = filteredHistory.filter(item => new Date(item.time) >= filterDate);
            }

            if (filteredHistory.length === 0) {
                historyList.innerHTML = '<div style="text-align: center; opacity: 0.7; padding: 20px;">No transactions found</div>';
                return;
            }

            filteredHistory.slice(-10).reverse().forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';

                if (item.type === 'purchase') {
                    const symbol = currencySymbols[item.currency] || '$';
                    historyItem.innerHTML = `
                        <span class="history-type">🛒</span>
                        <div class="history-details">
                            <div>Purchase - ${symbol}${item.amount.toFixed(2)}</div>
                            <div style="font-size: 12px; opacity: 0.7;">${item.time}</div>
                        </div>
                        <div class="history-amount">+${item.units.toFixed(1)} ${unitType}</div>
                    `;
                } else {
                    historyItem.innerHTML = `
                        <span class="history-type">📈</span>
                        <div class="history-details">
                            <div>Usage Recorded</div>
                            <div style="font-size: 12px; opacity: 0.7;">${item.time}</div>
                        </div>
                        <div class="history-amount">-${item.units.toFixed(1)} ${unitType}</div>
                    `;
                }
                historyList.appendChild(historyItem);
            });
        }

        function filterHistory() { updateHistory(); }

        function addPurchase() {
            const amount = parseFloat(document.getElementById('purchaseAmount').value);
            const units = parseFloat(document.getElementById('purchaseUnits').value);
            const currency = document.getElementById('currency').value;

            if (amount && units) {
                currentUnits += units;
                history.push({
                    type: 'purchase',
                    amount: amount,
                    units: units,
                    currency: currency,
                    time: new Date().toLocaleString()
                });

                document.getElementById('purchaseAmount').value = '';
                document.getElementById('purchaseUnits').value = '';

                updateDisplay();
                updateHistory();
                showNotification(`✅ Purchase added! +${units.toFixed(1)} ${unitType} for ${currencySymbols[currency]}${amount.toFixed(2)}`);
            } else {
                showNotification('❌ Please enter both amount and units');
            }
        }

        function recordUsage() {
            const reading = parseFloat(document.getElementById('currentReading').value);
            const usage = parseFloat(document.getElementById('usageAmount').value);

            if (usage && usage > 0) {
                currentUnits = reading;
                previousReading = reading + usage;

                history.push({
                    type: 'usage',
                    units: usage,
                    time: new Date().toLocaleString()
                });

                document.getElementById('currentReading').value = '';
                document.getElementById('usageAmount').value = '';

                updateDisplay();
                updateHistory();
                showNotification(`✅ Usage recorded! -${usage.toFixed(1)} ${unitType} used`);

                if (currentUnits <= lowUnitsThreshold) {
                    setTimeout(() => showNotification('⚠️ Warning: Low units remaining!'), 1000);
                }
            } else {
                showNotification('❌ Please enter current reading');
            }
        }

        function updateUnitCost() {
            unitCost = parseFloat(document.getElementById('unitCost').value) || 0.15;
            updatePurchaseCalculation();
        }

        function updateThreshold() {
            lowUnitsThreshold = parseFloat(document.getElementById('lowUnitsThreshold').value) || 20;
            updateDisplay();
        }

        function updateDefaultCurrency() {
            defaultCurrency = document.getElementById('defaultCurrency').value;
            document.getElementById('currency').value = defaultCurrency;
            updateDisplay();
        }

        function updateUnitType() {
            unitType = document.getElementById('unitType').value;
            updateDisplay();
            updateHistory();
        }

        function updatePurchaseCalculation() {
            const amount = parseFloat(document.getElementById('purchaseAmount').value);
            if (amount) {
                const units = amount / unitCost;
                document.getElementById('purchaseUnits').value = units.toFixed(2);
            }
        }

        function updateUsageCalculation() {
            const reading = parseFloat(document.getElementById('currentReading').value);
            if (reading) {
                const usage = Math.max(0, previousReading - reading);
                document.getElementById('usageAmount').value = usage.toFixed(1);
            }
        }

        function resetData() {
            if (confirm('Are you sure you want to reset all data? This cannot be undone.')) {
                currentUnits = 0;
                previousReading = 0;
                history = [];
                updateDisplay();
                updateHistory();
                showNotification('🔄 All data has been reset');
            }
        }

        // Event listeners
        document.getElementById('purchaseAmount').addEventListener('input', updatePurchaseCalculation);
        document.getElementById('currentReading').addEventListener('input', updateUsageCalculation);

        // Initialize app
        document.getElementById('currency').value = defaultCurrency;
        document.getElementById('unitCost').value = unitCost;
        document.getElementById('lowUnitsThreshold').value = lowUnitsThreshold;
        document.getElementById('defaultCurrency').value = defaultCurrency;
        document.getElementById('unitType').value = unitType;

        updateDisplay();
        updateHistory();

        // Welcome message
        setTimeout(() => showNotification('🎉 Welcome to Prepaid User Electricity!'), 1000);
    </script>
</body>
</html>
