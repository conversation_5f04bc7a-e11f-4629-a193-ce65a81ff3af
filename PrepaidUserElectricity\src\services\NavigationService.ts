import { NavigationContainerRef } from '@react-navigation/native';
import { DrawerParamList } from '../navigation/AppNavigator';

class NavigationService {
  private navigationRef: React.RefObject<NavigationContainerRef<DrawerParamList>> | null = null;

  setNavigationRef(ref: React.RefObject<NavigationContainerRef<DrawerParamList>>) {
    this.navigationRef = ref;
  }

  navigate(routeName: keyof DrawerParamList, params?: any) {
    if (this.navigationRef?.current) {
      this.navigationRef.current.navigate(routeName as any, params);
    }
  }

  goBack() {
    if (this.navigationRef?.current) {
      this.navigationRef.current.goBack();
    }
  }

  reset(routeName: keyof DrawerParamList) {
    if (this.navigationRef?.current) {
      this.navigationRef.current.reset({
        index: 0,
        routes: [{ name: routeName as any }],
      });
    }
  }

  // Handle notification navigation
  handleNotificationNavigation(notificationType: string) {
    switch (notificationType) {
      case 'daily_reminder':
      case 'usage_confirmation':
        this.navigate('Usage');
        break;
      
      case 'low_units_warning':
      case 'purchase_confirmation':
        this.navigate('Purchases');
        break;
      
      default:
        this.navigate('Dashboard');
    }
  }
}

export default new NavigationService();
