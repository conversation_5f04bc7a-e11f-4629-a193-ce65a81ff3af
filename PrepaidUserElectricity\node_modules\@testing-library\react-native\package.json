{"name": "@testing-library/react-native", "version": "13.2.0", "description": "Simple and complete React Native testing utilities that encourage good testing practices.", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "https://www.github.com/callstack/react-native-testing-library.git"}, "homepage": "https://callstack.github.io/react-native-testing-library", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/thymikee), <PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/mdjastrzebski)", "contributors": ["Augustin <PERSON> <<EMAIL>> (https://github.com/AugustinLF)", "<PERSON> <<EMAIL>> (https://github.com/pierrez<PERSON>mannbam)", "MattAgn <<EMAIL>> (https://github.com/MattAgn)"], "license": "MIT", "keywords": ["react-native", "react", "test", "integration"], "scripts": {"clean": "del build", "test": "jest", "test:ci": "jest --maxWorkers=2", "test:ci:coverage": "jest --maxWorkers=2 --collectCoverage=true --coverage-provider=v8", "typecheck": "tsc", "copy-flowtypes": "cp typings/index.flow.js build", "lint": "eslint src --cache", "validate": "yarn lint && yarn typecheck && yarn test", "build:js": "babel src --out-dir build --extensions \".js,.ts,.jsx,.tsx\" --source-maps --ignore \"**/__tests__/**\"", "build:ts": "tsc --build tsconfig.release.json", "build": "yarn clean && yarn build:js && yarn build:ts && yarn copy-flowtypes", "release": "release-it", "release:rc": "release-it --preRelease=rc"}, "files": ["build/", "matchers.js", "matchers.d.ts", "pure.js", "pure.d.ts", "dont-cleanup-after-each.js", "typings/index.flow.js"], "dependencies": {"chalk": "^4.1.2", "jest-matcher-utils": "^29.7.0", "pretty-format": "^29.7.0", "redent": "^3.0.0"}, "peerDependencies": {"jest": ">=29.0.0", "react": ">=18.2.0", "react-native": ">=0.71", "react-test-renderer": ">=18.2.0"}, "peerDependenciesMeta": {"jest": {"optional": true}}, "devDependencies": {"@babel/cli": "^7.25.9", "@babel/core": "^7.26.0", "@babel/plugin-transform-strict-mode": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-flow": "^7.25.9", "@babel/preset-react": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@callstack/eslint-config": "^15.0.0", "@react-native/babel-preset": "0.78.0", "@release-it/conventional-changelog": "^10.0.0", "@relmify/jest-serializer-strip-ansi": "^1.0.2", "@types/jest": "^29.5.14", "@types/node": "^22.13.8", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "babel-jest": "^29.7.0", "babel-plugin-module-resolver": "^5.0.2", "del-cli": "^6.0.0", "eslint": "^9.17.0", "eslint-plugin-simple-import-sort": "^12.1.1", "flow-bin": "~0.170.0", "jest": "^29.7.0", "prettier": "^2.8.8", "react": "19.0.0", "react-native": "0.78.0", "react-native-gesture-handler": "^2.23.1", "react-test-renderer": "19.0.0", "release-it": "^18.0.0", "typescript": "^5.6.3", "typescript-eslint": "^8.19.1"}, "publishConfig": {"registry": "https://registry.npmjs.org"}, "packageManager": "yarn@4.6.0", "engines": {"node": ">=18"}}