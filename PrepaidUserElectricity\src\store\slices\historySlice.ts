import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import DatabaseManager, { HistoryEntry } from '../../database/DatabaseManager';

export interface HistoryState {
  history: HistoryEntry[];
  filteredHistory: HistoryEntry[];
  isLoading: boolean;
  error: string | null;
  filter: 'all' | 'weekly' | 'monthly';
  typeFilter: 'all' | 'purchase' | 'usage';
}

const initialState: HistoryState = {
  history: [],
  filteredHistory: [],
  isLoading: false,
  error: null,
  filter: 'all',
  typeFilter: 'all',
};

// Async thunks
export const loadHistory = createAsyncThunk(
  'history/loadHistory',
  async (_, { rejectWithValue }) => {
    try {
      const history = await DatabaseManager.getHistory();
      return history;
    } catch (error) {
      return rejectWithValue('Failed to load history');
    }
  }
);

// Helper function to filter history by date range
const filterHistoryByDate = (history: HistoryEntry[], filter: 'all' | 'weekly' | 'monthly'): HistoryEntry[] => {
  if (filter === 'all') return history;

  const now = new Date();
  let cutoffDate: Date;

  if (filter === 'weekly') {
    cutoffDate = new Date();
    cutoffDate.setDate(now.getDate() - 7);
  } else {
    cutoffDate = new Date();
    cutoffDate.setMonth(now.getMonth() - 1);
  }

  return history.filter(entry => new Date(entry.timestamp) >= cutoffDate);
};

// Helper function to filter history by type
const filterHistoryByType = (history: HistoryEntry[], typeFilter: 'all' | 'purchase' | 'usage'): HistoryEntry[] => {
  if (typeFilter === 'all') return history;
  return history.filter(entry => entry.type === typeFilter);
};

const historySlice = createSlice({
  name: 'history',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilter: (state, action: PayloadAction<'all' | 'weekly' | 'monthly'>) => {
      state.filter = action.payload;
      // Apply filters
      let filtered = filterHistoryByDate(state.history, action.payload);
      filtered = filterHistoryByType(filtered, state.typeFilter);
      state.filteredHistory = filtered;
    },
    setTypeFilter: (state, action: PayloadAction<'all' | 'purchase' | 'usage'>) => {
      state.typeFilter = action.payload;
      // Apply filters
      let filtered = filterHistoryByDate(state.history, state.filter);
      filtered = filterHistoryByType(filtered, action.payload);
      state.filteredHistory = filtered;
    },
    applyFilters: (state) => {
      let filtered = filterHistoryByDate(state.history, state.filter);
      filtered = filterHistoryByType(filtered, state.typeFilter);
      state.filteredHistory = filtered;
    },
    setHistory: (state, action: PayloadAction<HistoryEntry[]>) => {
      state.history = action.payload;
      // Apply current filters to new history
      let filtered = filterHistoryByDate(action.payload, state.filter);
      filtered = filterHistoryByType(filtered, state.typeFilter);
      state.filteredHistory = filtered;
    },
  },
  extraReducers: (builder) => {
    builder
      // Load history
      .addCase(loadHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.history = action.payload;
        // Apply current filters
        let filtered = filterHistoryByDate(action.payload, state.filter);
        filtered = filterHistoryByType(filtered, state.typeFilter);
        state.filteredHistory = filtered;
      })
      .addCase(loadHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setFilter,
  setTypeFilter,
  applyFilters,
  setHistory,
} = historySlice.actions;

export default historySlice.reducer;
