{"version": 3, "file": "to-be-selected.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_accessibility", "_formatElement", "_utils", "e", "__esModule", "default", "toBeSelected", "element", "checkHostElement", "pass", "computeAriaSelected", "message", "is", "isNot", "matcherHint", "redent", "formatElement", "join"], "sources": ["../../src/matchers/to-be-selected.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { computeAriaSelected } from '../helpers/accessibility';\nimport { formatElement } from '../helpers/format-element';\nimport { checkHostElement } from './utils';\n\nexport function toBeSelected(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeSelected, this);\n\n  return {\n    pass: computeAriaSelected(element),\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeSelected`, 'element', ''),\n        '',\n        `Received element ${is} selected`,\n        redent(formatElement(element), 2),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAA2C,SAAAE,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,YAAYA,CAA4BC,OAA0B,EAAE;EAClF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,YAAY,EAAE,IAAI,CAAC;EAE7C,OAAO;IACLG,IAAI,EAAE,IAAAC,kCAAmB,EAACH,OAAO,CAAC;IAClCI,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,eAAe,EAAE,SAAS,EAAE,EAAE,CAAC,EACtE,EAAE,EACF,oBAAoBD,EAAE,WAAW,EACjC,IAAAG,eAAM,EAAC,IAAAC,4BAAa,EAACT,OAAO,CAAC,EAAE,CAAC,CAAC,CAClC,CAACU,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}