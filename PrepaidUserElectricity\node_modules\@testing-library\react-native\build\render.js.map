{"version": 3, "file": "render.js", "names": ["React", "_interopRequireWildcard", "require", "_act", "_interopRequireDefault", "_cleanup", "_config", "_componentTree", "_debug", "_stringValidation", "_renderAct", "_screen", "_within", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "render", "component", "options", "renderInternal", "wrapper", "Wrapper", "concurrentRoot", "unstable_validateStringsRenderedWithinText", "rest", "testRendererOptions", "unstable_isConcurrent", "getConfig", "renderWithStringValidation", "wrap", "element", "createElement", "renderer", "renderWithAct", "buildRenderResult", "Profiler", "id", "onRender", "handleRender", "_", "phase", "validateStringsRenderedWithinText", "toJSON", "update", "updateWithAct", "instance", "root", "unmount", "act", "addToCleanupQueue", "result", "getQueriesForElement", "rerender", "debug", "makeDebug", "getHostSelves", "UNSAFE_root", "enumerable", "Error", "setRenderResult", "debugImpl", "defaultDebugOptions", "debugOptions", "json"], "sources": ["../src/render.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type {\n  ReactTestInstance,\n  ReactTestRenderer,\n  TestRendererOptions,\n} from 'react-test-renderer';\n\nimport act from './act';\nimport { addToCleanupQueue } from './cleanup';\nimport { getConfig } from './config';\nimport { getHostSelves } from './helpers/component-tree';\nimport type { DebugOptions } from './helpers/debug';\nimport { debug } from './helpers/debug';\nimport { validateStringsRenderedWithinText } from './helpers/string-validation';\nimport { renderWithAct } from './render-act';\nimport { setRenderResult } from './screen';\nimport { getQueriesForElement } from './within';\n\nexport interface RenderOptions {\n  /**\n   * Pass a React Component as the wrapper option to have it rendered around the inner element. This is most useful for creating\n   * reusable custom render functions for common data providers.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  wrapper?: React.ComponentType<any>;\n\n  /**\n   * Set to `false` to disable concurrent rendering.\n   * Otherwise `render` will default to concurrent rendering.\n   */\n  concurrentRoot?: boolean;\n\n  createNodeMock?: (element: React.ReactElement) => unknown;\n  unstable_validateStringsRenderedWithinText?: boolean;\n}\n\nexport type RenderResult = ReturnType<typeof render>;\n\n/**\n * Renders test component deeply using React Test Renderer and exposes helpers\n * to assert on the output.\n */\nexport default function render<T>(component: React.ReactElement<T>, options: RenderOptions = {}) {\n  return renderInternal(component, options);\n}\n\nexport function renderInternal<T>(component: React.ReactElement<T>, options?: RenderOptions) {\n  const {\n    wrapper: Wrapper,\n    concurrentRoot,\n    unstable_validateStringsRenderedWithinText,\n    ...rest\n  } = options || {};\n\n  const testRendererOptions: TestRendererOptions = {\n    ...rest,\n    // @ts-expect-error incomplete typing on RTR package\n    unstable_isConcurrent: concurrentRoot ?? getConfig().concurrentRoot,\n  };\n\n  if (unstable_validateStringsRenderedWithinText) {\n    return renderWithStringValidation(component, {\n      wrapper: Wrapper,\n      ...testRendererOptions,\n    });\n  }\n\n  const wrap = (element: React.ReactElement) => (Wrapper ? <Wrapper>{element}</Wrapper> : element);\n  const renderer = renderWithAct(wrap(component), testRendererOptions);\n  return buildRenderResult(renderer, wrap);\n}\n\nfunction renderWithStringValidation<T>(\n  component: React.ReactElement<T>,\n  options: Omit<RenderOptions, 'unstable_validateStringsRenderedWithinText'> = {},\n) {\n  const { wrapper: Wrapper, ...testRendererOptions } = options ?? {};\n\n  const wrap = (element: React.ReactElement) => (\n    <React.Profiler id=\"renderProfiler\" onRender={handleRender}>\n      {Wrapper ? <Wrapper>{element}</Wrapper> : element}\n    </React.Profiler>\n  );\n\n  const handleRender: React.ProfilerOnRenderCallback = (_, phase) => {\n    if (renderer && phase === 'update') {\n      validateStringsRenderedWithinText(renderer.toJSON());\n    }\n  };\n\n  const renderer: ReactTestRenderer = renderWithAct(wrap(component), testRendererOptions);\n  validateStringsRenderedWithinText(renderer.toJSON());\n\n  return buildRenderResult(renderer, wrap);\n}\n\nfunction buildRenderResult(\n  renderer: ReactTestRenderer,\n  wrap: (element: React.ReactElement) => React.JSX.Element,\n) {\n  const update = updateWithAct(renderer, wrap);\n  const instance = renderer.root;\n\n  const unmount = () => {\n    void act(() => {\n      renderer.unmount();\n    });\n  };\n\n  addToCleanupQueue(unmount);\n\n  const result = {\n    ...getQueriesForElement(instance),\n    update,\n    unmount,\n    rerender: update, // alias for `update`\n    toJSON: renderer.toJSON,\n    debug: makeDebug(renderer),\n    get root(): ReactTestInstance {\n      return getHostSelves(instance)[0];\n    },\n    UNSAFE_root: instance,\n  };\n\n  // Add as non-enumerable property, so that it's safe to enumerate\n  // `render` result, e.g. using destructuring rest syntax.\n  Object.defineProperty(result, 'container', {\n    enumerable: false,\n    get() {\n      throw new Error(\n        \"'container' property has been renamed to 'UNSAFE_root'.\\n\\n\" +\n          \"Consider using 'root' property which returns root host element.\",\n      );\n    },\n  });\n\n  setRenderResult(result);\n\n  return result;\n}\n\nfunction updateWithAct(\n  renderer: ReactTestRenderer,\n  wrap: (innerElement: React.ReactElement) => React.ReactElement,\n) {\n  return function (component: React.ReactElement) {\n    void act(() => {\n      renderer.update(wrap(component));\n    });\n  };\n}\n\nexport type DebugFunction = (options?: DebugOptions) => void;\n\nfunction makeDebug(renderer: ReactTestRenderer): DebugFunction {\n  function debugImpl(options?: DebugOptions) {\n    const { defaultDebugOptions } = getConfig();\n    const debugOptions = { ...defaultDebugOptions, ...options };\n    const json = renderer.toJSON();\n    if (json) {\n      return debug(json, debugOptions);\n    }\n  }\n  return debugImpl;\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAOA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AAEA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,iBAAA,GAAAP,OAAA;AACA,IAAAQ,UAAA,GAAAR,OAAA;AACA,IAAAS,OAAA,GAAAT,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AAAgD,SAAAE,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAZ,wBAAAY,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAsBhD;AACA;AACA;AACA;AACe,SAASW,MAAMA,CAAIC,SAAgC,EAAEC,OAAsB,GAAG,CAAC,CAAC,EAAE;EAC/F,OAAOC,cAAc,CAACF,SAAS,EAAEC,OAAO,CAAC;AAC3C;AAEO,SAASC,cAAcA,CAAIF,SAAgC,EAAEC,OAAuB,EAAE;EAC3F,MAAM;IACJE,OAAO,EAAEC,OAAO;IAChBC,cAAc;IACdC,0CAA0C;IAC1C,GAAGC;EACL,CAAC,GAAGN,OAAO,IAAI,CAAC,CAAC;EAEjB,MAAMO,mBAAwC,GAAG;IAC/C,GAAGD,IAAI;IACP;IACAE,qBAAqB,EAAEJ,cAAc,IAAI,IAAAK,iBAAS,EAAC,CAAC,CAACL;EACvD,CAAC;EAED,IAAIC,0CAA0C,EAAE;IAC9C,OAAOK,0BAA0B,CAACX,SAAS,EAAE;MAC3CG,OAAO,EAAEC,OAAO;MAChB,GAAGI;IACL,CAAC,CAAC;EACJ;EAEA,MAAMI,IAAI,GAAIC,OAA2B,IAAMT,OAAO,gBAAGtC,KAAA,CAAAgD,aAAA,CAACV,OAAO,QAAES,OAAiB,CAAC,GAAGA,OAAQ;EAChG,MAAME,QAAQ,GAAG,IAAAC,wBAAa,EAACJ,IAAI,CAACZ,SAAS,CAAC,EAAEQ,mBAAmB,CAAC;EACpE,OAAOS,iBAAiB,CAACF,QAAQ,EAAEH,IAAI,CAAC;AAC1C;AAEA,SAASD,0BAA0BA,CACjCX,SAAgC,EAChCC,OAA0E,GAAG,CAAC,CAAC,EAC/E;EACA,MAAM;IAAEE,OAAO,EAAEC,OAAO;IAAE,GAAGI;EAAoB,CAAC,GAAGP,OAAO,IAAI,CAAC,CAAC;EAElE,MAAMW,IAAI,GAAIC,OAA2B,iBACvC/C,KAAA,CAAAgD,aAAA,CAAChD,KAAK,CAACoD,QAAQ;IAACC,EAAE,EAAC,gBAAgB;IAACC,QAAQ,EAAEC;EAAa,GACxDjB,OAAO,gBAAGtC,KAAA,CAAAgD,aAAA,CAACV,OAAO,QAAES,OAAiB,CAAC,GAAGA,OAC5B,CACjB;EAED,MAAMQ,YAA4C,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;IACjE,IAAIR,QAAQ,IAAIQ,KAAK,KAAK,QAAQ,EAAE;MAClC,IAAAC,mDAAiC,EAACT,QAAQ,CAACU,MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMV,QAA2B,GAAG,IAAAC,wBAAa,EAACJ,IAAI,CAACZ,SAAS,CAAC,EAAEQ,mBAAmB,CAAC;EACvF,IAAAgB,mDAAiC,EAACT,QAAQ,CAACU,MAAM,CAAC,CAAC,CAAC;EAEpD,OAAOR,iBAAiB,CAACF,QAAQ,EAAEH,IAAI,CAAC;AAC1C;AAEA,SAASK,iBAAiBA,CACxBF,QAA2B,EAC3BH,IAAwD,EACxD;EACA,MAAMc,MAAM,GAAGC,aAAa,CAACZ,QAAQ,EAAEH,IAAI,CAAC;EAC5C,MAAMgB,QAAQ,GAAGb,QAAQ,CAACc,IAAI;EAE9B,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,KAAK,IAAAC,YAAG,EAAC,MAAM;MACbhB,QAAQ,CAACe,OAAO,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,IAAAE,0BAAiB,EAACF,OAAO,CAAC;EAE1B,MAAMG,MAAM,GAAG;IACb,GAAG,IAAAC,4BAAoB,EAACN,QAAQ,CAAC;IACjCF,MAAM;IACNI,OAAO;IACPK,QAAQ,EAAET,MAAM;IAAE;IAClBD,MAAM,EAAEV,QAAQ,CAACU,MAAM;IACvBW,KAAK,EAAEC,SAAS,CAACtB,QAAQ,CAAC;IAC1B,IAAIc,IAAIA,CAAA,EAAsB;MAC5B,OAAO,IAAAS,4BAAa,EAACV,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACDW,WAAW,EAAEX;EACf,CAAC;;EAED;EACA;EACArC,MAAM,CAACC,cAAc,CAACyC,MAAM,EAAE,WAAW,EAAE;IACzCO,UAAU,EAAE,KAAK;IACjBrD,GAAGA,CAAA,EAAG;MACJ,MAAM,IAAIsD,KAAK,CACb,6DAA6D,GAC3D,iEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAEF,IAAAC,uBAAe,EAACT,MAAM,CAAC;EAEvB,OAAOA,MAAM;AACf;AAEA,SAASN,aAAaA,CACpBZ,QAA2B,EAC3BH,IAA8D,EAC9D;EACA,OAAO,UAAUZ,SAA6B,EAAE;IAC9C,KAAK,IAAA+B,YAAG,EAAC,MAAM;MACbhB,QAAQ,CAACW,MAAM,CAACd,IAAI,CAACZ,SAAS,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;AACH;AAIA,SAASqC,SAASA,CAACtB,QAA2B,EAAiB;EAC7D,SAAS4B,SAASA,CAAC1C,OAAsB,EAAE;IACzC,MAAM;MAAE2C;IAAoB,CAAC,GAAG,IAAAlC,iBAAS,EAAC,CAAC;IAC3C,MAAMmC,YAAY,GAAG;MAAE,GAAGD,mBAAmB;MAAE,GAAG3C;IAAQ,CAAC;IAC3D,MAAM6C,IAAI,GAAG/B,QAAQ,CAACU,MAAM,CAAC,CAAC;IAC9B,IAAIqB,IAAI,EAAE;MACR,OAAO,IAAAV,YAAK,EAACU,IAAI,EAAED,YAAY,CAAC;IAClC;EACF;EACA,OAAOF,SAAS;AAClB", "ignoreList": []}