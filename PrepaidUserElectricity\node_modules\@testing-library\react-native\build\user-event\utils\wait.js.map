{"version": 3, "file": "wait.js", "names": ["wait", "config", "durationInMs", "delay", "Promise", "all", "resolve", "globalThis", "setTimeout", "advanceTimers"], "sources": ["../../../src/user-event/utils/wait.ts"], "sourcesContent": ["type WaitConfig = {\n  delay?: number;\n  advanceTimers: (delay: number) => Promise<void> | void;\n};\n\nexport function wait(config: WaitConfig, durationInMs?: number) {\n  const delay = durationInMs ?? config.delay;\n  if (typeof delay !== 'number' || delay == null) {\n    return;\n  }\n\n  return Promise.all([\n    new Promise<void>((resolve) => globalThis.setTimeout(() => resolve(), delay)),\n    config.advanceTimers(delay),\n  ]);\n}\n"], "mappings": ";;;;;;AAKO,SAASA,IAAIA,CAACC,MAAkB,EAAEC,YAAqB,EAAE;EAC9D,MAAMC,KAAK,GAAGD,YAAY,IAAID,MAAM,CAACE,KAAK;EAC1C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC9C;EACF;EAEA,OAAOC,OAAO,CAACC,GAAG,CAAC,CACjB,IAAID,OAAO,CAAQE,OAAO,IAAKC,UAAU,CAACC,UAAU,CAAC,MAAMF,OAAO,CAAC,CAAC,EAAEH,KAAK,CAAC,CAAC,EAC7EF,MAAM,CAACQ,aAAa,CAACN,KAAK,CAAC,CAC5B,CAAC;AACJ", "ignoreList": []}