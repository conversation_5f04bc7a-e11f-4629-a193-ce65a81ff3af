{"version": 3, "file": "matches.js", "names": ["matches", "matcher", "text", "normalizer", "getDefaultNormalizer", "exact", "normalizedText", "normalizedMatcher", "toLowerCase", "includes", "lastIndex", "test", "trim", "collapseWhitespace", "replace"], "sources": ["../src/matches.ts"], "sourcesContent": ["export type NormalizerFn = (textToNormalize: string) => string;\n\nexport type TextMatch = string | RegExp;\nexport type TextMatchOptions = {\n  exact?: boolean;\n  normalizer?: NormalizerFn;\n};\n\nexport function matches(\n  matcher: TextMatch,\n  text: string | undefined,\n  normalizer: NormalizerFn = getDefaultNormalizer(),\n  exact: boolean = true,\n): boolean {\n  if (typeof text !== 'string') {\n    return false;\n  }\n\n  const normalizedText = normalizer(text);\n  if (typeof matcher === 'string') {\n    const normalizedMatcher = normalizer(matcher);\n    return exact\n      ? normalizedText === normalizedMatcher\n      : normalizedText.toLowerCase().includes(normalizedMatcher.toLowerCase());\n  } else {\n    // Reset state for global regexes: https://stackoverflow.com/a/1520839/484499\n    matcher.lastIndex = 0;\n    return matcher.test(normalizedText);\n  }\n}\n\ntype NormalizerConfig = {\n  trim?: boolean;\n  collapseWhitespace?: boolean;\n};\n\nexport function getDefaultNormalizer({\n  trim = true,\n  collapseWhitespace = true,\n}: NormalizerConfig = {}): NormalizerFn {\n  return (text: string) => {\n    let normalizedText = text;\n    normalizedText = trim ? normalizedText.trim() : normalizedText;\n    normalizedText = collapseWhitespace ? normalizedText.replace(/\\s+/g, ' ') : normalizedText;\n    return normalizedText;\n  };\n}\n"], "mappings": ";;;;;;;AAQO,SAASA,OAAOA,CACrBC,OAAkB,EAClBC,IAAwB,EACxBC,UAAwB,GAAGC,oBAAoB,CAAC,CAAC,EACjDC,KAAc,GAAG,IAAI,EACZ;EACT,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO,KAAK;EACd;EAEA,MAAMI,cAAc,GAAGH,UAAU,CAACD,IAAI,CAAC;EACvC,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAMM,iBAAiB,GAAGJ,UAAU,CAACF,OAAO,CAAC;IAC7C,OAAOI,KAAK,GACRC,cAAc,KAAKC,iBAAiB,GACpCD,cAAc,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,iBAAiB,CAACC,WAAW,CAAC,CAAC,CAAC;EAC5E,CAAC,MAAM;IACL;IACAP,OAAO,CAACS,SAAS,GAAG,CAAC;IACrB,OAAOT,OAAO,CAACU,IAAI,CAACL,cAAc,CAAC;EACrC;AACF;AAOO,SAASF,oBAAoBA,CAAC;EACnCQ,IAAI,GAAG,IAAI;EACXC,kBAAkB,GAAG;AACL,CAAC,GAAG,CAAC,CAAC,EAAgB;EACtC,OAAQX,IAAY,IAAK;IACvB,IAAII,cAAc,GAAGJ,IAAI;IACzBI,cAAc,GAAGM,IAAI,GAAGN,cAAc,CAACM,IAAI,CAAC,CAAC,GAAGN,cAAc;IAC9DA,cAAc,GAAGO,kBAAkB,GAAGP,cAAc,CAACQ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GAAGR,cAAc;IAC1F,OAAOA,cAAc;EACvB,CAAC;AACH", "ignoreList": []}