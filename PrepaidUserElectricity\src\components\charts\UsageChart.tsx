import React from 'react';
import { View, Dimensions } from 'react-native';
import {
  Victory<PERSON>hart,
  VictoryLine,
  VictoryArea,
  VictoryAxis,
  VictoryTheme,
  VictoryTooltip,
  VictoryScatter,
} from 'victory-native';
import { useTheme } from '../../theme/ThemeContext';
import { ThemedText } from '../common/StyledComponents';

interface UsageDataPoint {
  x: number;
  y: number;
  label?: string;
  date?: string;
}

interface UsageChartProps {
  data: UsageDataPoint[];
  title?: string;
  height?: number;
  showArea?: boolean;
  showPoints?: boolean;
  color?: string;
}

const UsageChart: React.FC<UsageChartProps> = ({
  data,
  title = 'Usage Trend',
  height = 200,
  showArea = true,
  showPoints = true,
  color,
}) => {
  const { theme } = useTheme();
  const screenWidth = Dimensions.get('window').width;
  
  const chartColor = color || theme.colors.primary;
  
  if (data.length === 0) {
    return (
      <View style={{ height, justifyContent: 'center', alignItems: 'center' }}>
        <ThemedText color="textSecondary">No data available</ThemedText>
      </View>
    );
  }
  
  return (
    <View>
      {title && (
        <ThemedText variant="h3" style={{ marginBottom: 8, textAlign: 'center' }}>
          {title}
        </ThemedText>
      )}
      
      <VictoryChart
        theme={VictoryTheme.material}
        width={screenWidth - 64}
        height={height}
        padding={{ left: 60, top: 20, right: 40, bottom: 60 }}
        domainPadding={{ x: 20 }}
      >
        <VictoryAxis
          dependentAxis
          style={{
            axis: { stroke: theme.colors.border },
            tickLabels: { fill: theme.colors.textSecondary, fontSize: 12 },
            grid: { stroke: theme.colors.border, strokeOpacity: 0.3 },
          }}
        />
        <VictoryAxis
          style={{
            axis: { stroke: theme.colors.border },
            tickLabels: { fill: theme.colors.textSecondary, fontSize: 12 },
            grid: { stroke: theme.colors.border, strokeOpacity: 0.3 },
          }}
        />
        
        {showArea && (
          <VictoryArea
            data={data}
            style={{
              data: {
                fill: chartColor,
                fillOpacity: 0.3,
                stroke: chartColor,
                strokeWidth: 2,
              },
            }}
            animate={{
              duration: 1000,
              onLoad: { duration: 500 },
            }}
          />
        )}
        
        <VictoryLine
          data={data}
          style={{
            data: {
              stroke: chartColor,
              strokeWidth: 3,
            },
          }}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
        />
        
        {showPoints && (
          <VictoryScatter
            data={data}
            size={4}
            style={{
              data: {
                fill: chartColor,
                stroke: '#fff',
                strokeWidth: 2,
              },
            }}
            labelComponent={<VictoryTooltip />}
            animate={{
              duration: 1000,
              onLoad: { duration: 500 },
            }}
          />
        )}
      </VictoryChart>
    </View>
  );
};

export default UsageChart;
