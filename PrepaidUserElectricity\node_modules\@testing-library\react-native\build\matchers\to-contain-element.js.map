{"version": 3, "file": "to-contain-element.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_formatElement", "_utils", "e", "__esModule", "default", "toContainElement", "container", "element", "checkHostElement", "matches", "findAll", "node", "pass", "length", "message", "matcherHint", "isNot", "RECEIVED_COLOR", "redent", "formatElement", "join"], "sources": ["../../src/matchers/to-contain-element.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, RECEIVED_COLOR } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { formatElement } from '../helpers/format-element';\nimport { checkHostElement } from './utils';\n\nexport function toContainElement(\n  this: jest.MatcherContext,\n  container: ReactTestInstance,\n  element: ReactTestInstance | null,\n) {\n  checkHostElement(container, toContainElement, this);\n\n  if (element !== null) {\n    checkHostElement(element, toContainElement, this);\n  }\n\n  let matches: ReactTestInstance[] = [];\n  if (element) {\n    matches = container.findAll((node) => node === element);\n  }\n\n  return {\n    pass: matches.length > 0,\n    message: () => {\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toContainElement`, 'container', 'element'),\n        '',\n        RECEIVED_COLOR(`${redent(formatElement(container), 2)} ${\n          this.isNot ? '\\n\\ncontains:\\n\\n' : '\\n\\ndoes not contain:\\n\\n'\n        } ${redent(formatElement(element), 2)}\n        `),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAA2C,SAAAE,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,gBAAgBA,CAE9BC,SAA4B,EAC5BC,OAAiC,EACjC;EACA,IAAAC,uBAAgB,EAACF,SAAS,EAAED,gBAAgB,EAAE,IAAI,CAAC;EAEnD,IAAIE,OAAO,KAAK,IAAI,EAAE;IACpB,IAAAC,uBAAgB,EAACD,OAAO,EAAEF,gBAAgB,EAAE,IAAI,CAAC;EACnD;EAEA,IAAII,OAA4B,GAAG,EAAE;EACrC,IAAIF,OAAO,EAAE;IACXE,OAAO,GAAGH,SAAS,CAACI,OAAO,CAAEC,IAAI,IAAKA,IAAI,KAAKJ,OAAO,CAAC;EACzD;EAEA,OAAO;IACLK,IAAI,EAAEH,OAAO,CAACI,MAAM,GAAG,CAAC;IACxBC,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,mBAAmB,EAAE,WAAW,EAAE,SAAS,CAAC,EACnF,EAAE,EACF,IAAAC,gCAAc,EAAC,GAAG,IAAAC,eAAM,EAAC,IAAAC,4BAAa,EAACb,SAAS,CAAC,EAAE,CAAC,CAAC,IACnD,IAAI,CAACU,KAAK,GAAG,mBAAmB,GAAG,2BAA2B,IAC5D,IAAAE,eAAM,EAAC,IAAAC,4BAAa,EAACZ,OAAO,CAAC,EAAE,CAAC,CAAC;AAC7C,SAAS,CAAC,CACH,CAACa,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}