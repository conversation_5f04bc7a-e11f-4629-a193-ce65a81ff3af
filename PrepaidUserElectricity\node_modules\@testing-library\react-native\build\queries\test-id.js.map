{"version": 3, "file": "test-id.js", "names": ["_findAll", "require", "_matches", "_makeQueries", "matchTestId", "node", "testId", "options", "exact", "normalizer", "matches", "props", "testID", "queryAllByTestId", "instance", "queryAllByTestIdFn", "queryOptions", "findAll", "getMultipleError", "String", "getMissingError", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy", "makeQueries", "bindByTestIdQueries", "getByTestId", "getAllByTestId", "queryByTestId", "findByTestId", "findAllByTestId", "exports"], "sources": ["../../src/queries/test-id.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { findAll } from '../helpers/find-all';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport { matches } from '../matches';\nimport type {\n  FindAllByQuery,\n  FindByQuery,\n  GetAllByQuery,\n  GetByQuery,\n  QueryAllByQuery,\n  QueryByQuery,\n} from './make-queries';\nimport { makeQueries } from './make-queries';\nimport type { CommonQueryOptions } from './options';\n\ntype ByTestIdOptions = CommonQueryOptions & TextMatchOptions;\n\nconst matchTestId = (\n  node: ReactTestInstance,\n  testId: TextMatch,\n  options: TextMatchOptions = {},\n) => {\n  const { exact, normalizer } = options;\n  return matches(testId, node.props.testID, normalizer, exact);\n};\n\nconst queryAllByTestId = (\n  instance: ReactTestInstance,\n): QueryAllByQuery<TextMatch, ByTestIdOptions> =>\n  function queryAllByTestIdFn(testId, queryOptions) {\n    return findAll(instance, (node) => matchTestId(node, testId, queryOptions), queryOptions);\n  };\n\nconst getMultipleError = (testId: TextMatch) =>\n  `Found multiple elements with testID: ${String(testId)}`;\nconst getMissingError = (testId: TextMatch) =>\n  `Unable to find an element with testID: ${String(testId)}`;\n\nconst { getBy, getAllBy, queryBy, queryAllBy, findBy, findAllBy } = makeQueries(\n  queryAllByTestId,\n  getMissingError,\n  getMultipleError,\n);\n\nexport type ByTestIdQueries = {\n  getByTestId: GetByQuery<TextMatch, ByTestIdOptions>;\n  getAllByTestId: GetAllByQuery<TextMatch, ByTestIdOptions>;\n  queryByTestId: QueryByQuery<TextMatch, ByTestIdOptions>;\n  queryAllByTestId: QueryAllByQuery<TextMatch, ByTestIdOptions>;\n  findByTestId: FindByQuery<TextMatch, ByTestIdOptions>;\n  findAllByTestId: FindAllByQuery<TextMatch, ByTestIdOptions>;\n};\n\nexport const bindByTestIdQueries = (instance: ReactTestInstance): ByTestIdQueries => ({\n  getByTestId: getBy(instance),\n  getAllByTestId: getAllBy(instance),\n  queryByTestId: queryBy(instance),\n  queryAllByTestId: queryAllBy(instance),\n  findByTestId: findBy(instance),\n  findAllByTestId: findAllBy(instance),\n});\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AASA,IAAAE,YAAA,GAAAF,OAAA;AAKA,MAAMG,WAAW,GAAGA,CAClBC,IAAuB,EACvBC,MAAiB,EACjBC,OAAyB,GAAG,CAAC,CAAC,KAC3B;EACH,MAAM;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGF,OAAO;EACrC,OAAO,IAAAG,gBAAO,EAACJ,MAAM,EAAED,IAAI,CAACM,KAAK,CAACC,MAAM,EAAEH,UAAU,EAAED,KAAK,CAAC;AAC9D,CAAC;AAED,MAAMK,gBAAgB,GACpBC,QAA2B,IAE3B,SAASC,kBAAkBA,CAACT,MAAM,EAAEU,YAAY,EAAE;EAChD,OAAO,IAAAC,gBAAO,EAACH,QAAQ,EAAGT,IAAI,IAAKD,WAAW,CAACC,IAAI,EAAEC,MAAM,EAAEU,YAAY,CAAC,EAAEA,YAAY,CAAC;AAC3F,CAAC;AAEH,MAAME,gBAAgB,GAAIZ,MAAiB,IACzC,wCAAwCa,MAAM,CAACb,MAAM,CAAC,EAAE;AAC1D,MAAMc,eAAe,GAAId,MAAiB,IACxC,0CAA0Ca,MAAM,CAACb,MAAM,CAAC,EAAE;AAE5D,MAAM;EAAEe,KAAK;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAU,CAAC,GAAG,IAAAC,wBAAW,EAC7Ed,gBAAgB,EAChBO,eAAe,EACfF,gBACF,CAAC;AAWM,MAAMU,mBAAmB,GAAId,QAA2B,KAAuB;EACpFe,WAAW,EAAER,KAAK,CAACP,QAAQ,CAAC;EAC5BgB,cAAc,EAAER,QAAQ,CAACR,QAAQ,CAAC;EAClCiB,aAAa,EAAER,OAAO,CAACT,QAAQ,CAAC;EAChCD,gBAAgB,EAAEW,UAAU,CAACV,QAAQ,CAAC;EACtCkB,YAAY,EAAEP,MAAM,CAACX,QAAQ,CAAC;EAC9BmB,eAAe,EAAEP,SAAS,CAACZ,QAAQ;AACrC,CAAC,CAAC;AAACoB,OAAA,CAAAN,mBAAA,GAAAA,mBAAA", "ignoreList": []}