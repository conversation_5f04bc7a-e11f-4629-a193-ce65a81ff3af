{"logs": [{"outputFile": "com.prepaiduserelectricity.app-mergeDebugResources-26:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\32793288452e4e46b72ff0226ea01ae3\\transformed\\core-1.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "7231", "endColumns": "100", "endOffsets": "7327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\62a6d5f4699f9a58c84c13e5faffb8be\\transformed\\material-1.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2430,2480,2531,2597,2660,2728,2806,2867,2938,3005,3067,3146,3211,3294,3379,3453,3517,3593,3641,3705,3781,3859,3921,3985,4048,4128,4205,4281,4358,4412,4467", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2425,2475,2526,2592,2655,2723,2801,2862,2933,3000,3062,3141,3206,3289,3374,3448,3512,3588,3636,3700,3776,3854,3916,3980,4043,4123,4200,4276,4353,4407,4462,4531"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2863,2927,2989,3056,3126,3203,3297,3404,3477,3539,3617,3677,3737,3815,3876,3934,3990,4050,4108,4162,4247,4303,4361,4415,4480,4572,4646,4723,4843,4906,4969,5046,5096,5147,5213,5276,5344,5422,5483,5554,5621,5683,5762,5827,5910,5995,6069,6133,6209,6257,6321,6397,6475,6537,6601,6664,6744,6821,6897,6974,7028,7083", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "292,2922,2984,3051,3121,3198,3292,3399,3472,3534,3612,3672,3732,3810,3871,3929,3985,4045,4103,4157,4242,4298,4356,4410,4475,4567,4641,4718,4838,4901,4964,5041,5091,5142,5208,5271,5339,5417,5478,5549,5616,5678,5757,5822,5905,5990,6064,6128,6204,6252,6316,6392,6470,6532,6596,6659,6739,6816,6892,6969,7023,7078,7147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\74b37bc7dd1bf2c703c44454fb275d4d\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,7152", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,7226"}}]}]}