{"version": 3, "file": "react-versions.js", "names": ["React", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "checkReactVersionAtLeast", "major", "minor", "version", "undefined", "actual<PERSON><PERSON><PERSON>", "actualMinor", "split", "map", "Number"], "sources": ["../src/react-versions.ts"], "sourcesContent": ["import * as React from 'react';\n\nexport function checkReactVersionAtLeast(major: number, minor: number): boolean {\n  if (React.version === undefined) return false;\n  const [actualMajor, actualMinor] = React.version.split('.').map(Number);\n\n  return actualMajor > major || (actualMajor === major && actualMinor >= minor);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAExB,SAASW,wBAAwBA,CAACC,KAAa,EAAEC,KAAa,EAAW;EAC9E,IAAIzB,KAAK,CAAC0B,OAAO,KAAKC,SAAS,EAAE,OAAO,KAAK;EAC7C,MAAM,CAACC,WAAW,EAAEC,WAAW,CAAC,GAAG7B,KAAK,CAAC0B,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;EAEvE,OAAOJ,WAAW,GAAGJ,KAAK,IAAKI,WAAW,KAAKJ,KAAK,IAAIK,WAAW,IAAIJ,KAAM;AAC/E", "ignoreList": []}