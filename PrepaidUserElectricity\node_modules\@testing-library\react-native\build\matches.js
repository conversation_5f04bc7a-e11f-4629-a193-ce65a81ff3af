"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getDefaultNormalizer = getDefaultNormalizer;
exports.matches = matches;
function matches(matcher, text, normalizer = getDefaultNormalizer(), exact = true) {
  if (typeof text !== 'string') {
    return false;
  }
  const normalizedText = normalizer(text);
  if (typeof matcher === 'string') {
    const normalizedMatcher = normalizer(matcher);
    return exact ? normalizedText === normalizedMatcher : normalizedText.toLowerCase().includes(normalizedMatcher.toLowerCase());
  } else {
    // Reset state for global regexes: https://stackoverflow.com/a/1520839/484499
    matcher.lastIndex = 0;
    return matcher.test(normalizedText);
  }
}
function getDefaultNormalizer({
  trim = true,
  collapseWhitespace = true
} = {}) {
  return text => {
    let normalizedText = text;
    normalizedText = trim ? normalizedText.trim() : normalizedText;
    normalizedText = collapseWhitespace ? normalizedText.replace(/\s+/g, ' ') : normalizedText;
    return normalizedText;
  };
}
//# sourceMappingURL=matches.js.map