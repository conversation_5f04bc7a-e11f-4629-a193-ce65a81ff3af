import React from 'react';
import { View, Text, Dimensions } from 'react-native';
import Svg, { Circle, Path, Defs, LinearGradient, Stop } from 'react-native-svg';
import { useTheme } from '../../theme/ThemeContext';

interface ModernDialProps {
  currentValue: number;
  maxValue: number;
  minValue?: number;
  title: string;
  subtitle?: string;
  unit: string;
  size?: number;
  showWarning?: boolean;
}

const ModernDial: React.FC<ModernDialProps> = ({
  currentValue,
  maxValue,
  minValue = 0,
  title,
  subtitle,
  unit,
  size = 200,
  showWarning = false,
}) => {
  const { theme, font } = useTheme();
  
  // Calculate percentage for the dial
  const percentage = Math.min(Math.max((currentValue - minValue) / (maxValue - minValue), 0), 1);
  const angle = percentage * 270; // 270 degrees for 3/4 circle
  
  // SVG dimensions
  const center = size / 2;
  const radius = (size - 40) / 2;
  const strokeWidth = 12;
  
  // Calculate the path for the progress arc
  const startAngle = 135; // Start at bottom left
  const endAngle = startAngle + angle;
  
  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };
  
  const describeArc = (x: number, y: number, radius: number, startAngle: number, endAngle: number) => {
    const start = polarToCartesian(x, y, radius, endAngle);
    const end = polarToCartesian(x, y, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return [
      "M", start.x, start.y, 
      "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y
    ].join(" ");
  };
  
  const backgroundPath = describeArc(center, center, radius, 135, 405); // Full 270 degrees
  const progressPath = describeArc(center, center, radius, 135, endAngle);
  
  // Determine colors based on warning state
  const progressColors = showWarning 
    ? [theme.colors.warning, theme.colors.error]
    : theme.colors.gradient;
  
  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      <View style={{ position: 'relative' }}>
        <Svg width={size} height={size}>
          <Defs>
            <LinearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <Stop offset="0%" stopColor={progressColors[0]} />
              <Stop offset="100%" stopColor={progressColors[1]} />
            </LinearGradient>
          </Defs>
          
          {/* Background arc */}
          <Path
            d={backgroundPath}
            fill="none"
            stroke={theme.colors.border}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
          />
          
          {/* Progress arc */}
          <Path
            d={progressPath}
            fill="none"
            stroke="url(#progressGradient)"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
          />
          
          {/* Center dot */}
          <Circle
            cx={center}
            cy={center}
            r={4}
            fill={theme.colors.primary}
          />
        </Svg>
        
        {/* Center content */}
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text
            style={{
              fontSize: font.sizes.xxlarge + 8,
              fontWeight: font.weights.bold,
              color: showWarning ? theme.colors.warning : theme.colors.primary,
              fontFamily: font.fontFamily,
            }}
          >
            {currentValue.toFixed(1)}
          </Text>
          <Text
            style={{
              fontSize: font.sizes.small,
              color: theme.colors.textSecondary,
              fontFamily: font.fontFamily,
              marginTop: -4,
            }}
          >
            {unit}
          </Text>
        </View>
      </View>
      
      {/* Title and subtitle */}
      <View style={{ alignItems: 'center', marginTop: 16 }}>
        <Text
          style={{
            fontSize: font.sizes.large,
            fontWeight: font.weights.medium,
            color: theme.colors.text,
            fontFamily: font.fontFamily,
          }}
        >
          {title}
        </Text>
        {subtitle && (
          <Text
            style={{
              fontSize: font.sizes.small,
              color: theme.colors.textSecondary,
              fontFamily: font.fontFamily,
              marginTop: 4,
            }}
          >
            {subtitle}
          </Text>
        )}
      </View>
    </View>
  );
};

export default ModernDial;
