import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Modal,
} from 'react-native';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { loadSettings, updateSettings, resetSettings } from '../store/slices/settingsSlice';
import {
  Container,
  Card,
  ThemedText,
  ThemedTextInput,
  GradientButton,
} from '../components/common/StyledComponents';
import { useTheme } from '../theme/ThemeContext';
import { themes, fonts } from '../theme/themes';
import Icon from 'react-native-vector-icons/MaterialIcons';

const SettingsScreen = () => {
  const dispatch = useAppDispatch();
  const { theme, font, changeTheme, changeFont } = useTheme();

  const [activeSection, setActiveSection] = useState<'general' | 'appearance' | 'reset' | null>(null);
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [showUnitModal, setShowUnitModal] = useState(false);
  const [showThemeModal, setShowThemeModal] = useState(false);
  const [showFontModal, setShowFontModal] = useState(false);
  const [customCurrency, setCustomCurrency] = useState('');
  const [customUnit, setCustomUnit] = useState('');
  const [tempUnitCost, setTempUnitCost] = useState('');
  const [tempThreshold, setTempThreshold] = useState('');
  const [tempNotificationTime, setTempNotificationTime] = useState('');

  // Redux state
  const settings = useAppSelector((state) => state.settings.settings);
  const { availableCurrencies, availableUnits, availableThemes, availableFonts } = useAppSelector((state) => state.settings);

  useEffect(() => {
    dispatch(loadSettings());
  }, []);

  useEffect(() => {
    if (settings) {
      setTempUnitCost(settings.unitCost.toString());
      setTempThreshold(settings.thresholdLimit.toString());
      setTempNotificationTime(settings.notificationTime);
      setCustomCurrency(settings.customCurrencyName || '');
      setCustomUnit(settings.customUnitName || '');
    }
  }, [settings]);

  const handleUpdateSetting = async (key: string, value: any) => {
    try {
      await dispatch(updateSettings({ [key]: value }));
    } catch (error) {
      Alert.alert('Error', 'Failed to update setting. Please try again.');
    }
  };

  const handleCurrencyChange = async (currency: string) => {
    if (currency === 'Custom') {
      setShowCurrencyModal(true);
    } else {
      await handleUpdateSetting('currencyType', currency);
      await handleUpdateSetting('customCurrencyName', null);
    }
  };

  const handleCustomCurrencySubmit = async () => {
    if (!customCurrency.trim()) {
      Alert.alert('Invalid Input', 'Please enter a valid currency name.');
      return;
    }

    await handleUpdateSetting('currencyType', 'Custom');
    await handleUpdateSetting('customCurrencyName', customCurrency.trim());
    setShowCurrencyModal(false);
  };

  const handleUnitChange = async (unit: string) => {
    if (unit === 'Custom') {
      setShowUnitModal(true);
    } else {
      await handleUpdateSetting('unitType', unit);
      await handleUpdateSetting('customUnitName', null);
    }
  };

  const handleCustomUnitSubmit = async () => {
    if (!customUnit.trim()) {
      Alert.alert('Invalid Input', 'Please enter a valid unit name.');
      return;
    }

    await handleUpdateSetting('unitType', 'Custom');
    await handleUpdateSetting('customUnitName', customUnit.trim());
    setShowUnitModal(false);
  };

  const handleThemeChange = async (themeName: string) => {
    changeTheme(themeName);
    await handleUpdateSetting('theme', themeName);
    setShowThemeModal(false);
  };

  const handleFontChange = async (fontName: string) => {
    changeFont(fontName);
    await handleUpdateSetting('fontStyle', fontName);
    setShowFontModal(false);
  };

  const handleUnitCostUpdate = async () => {
    const value = parseFloat(tempUnitCost);
    if (isNaN(value) || value <= 0) {
      Alert.alert('Invalid Input', 'Please enter a valid unit cost.');
      return;
    }
    await handleUpdateSetting('unitCost', value);
  };

  const handleThresholdUpdate = async () => {
    const value = parseFloat(tempThreshold);
    if (isNaN(value) || value < 0) {
      Alert.alert('Invalid Input', 'Please enter a valid threshold limit.');
      return;
    }
    await handleUpdateSetting('thresholdLimit', value);
  };

  const handleNotificationTimeUpdate = async () => {
    // Simple time validation (HH:MM format)
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(tempNotificationTime)) {
      Alert.alert('Invalid Input', 'Please enter a valid time in HH:MM format.');
      return;
    }
    await handleUpdateSetting('notificationTime', tempNotificationTime);
  };

  const handleFactoryReset = () => {
    Alert.alert(
      'Factory Reset',
      'This will delete all data including purchases, usage records, and settings. This action cannot be undone. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(resetSettings('factory'));
              Alert.alert('Success', 'Factory reset completed successfully.');
            } catch (error) {
              Alert.alert('Error', 'Failed to perform factory reset.');
            }
          },
        },
      ]
    );
  };

  const handleDashboardReset = () => {
    Alert.alert(
      'Dashboard Data Reset',
      'This will delete usage records but keep purchase history and settings. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(resetSettings('dashboard'));
              Alert.alert('Success', 'Dashboard data reset completed successfully.');
            } catch (error) {
              Alert.alert('Error', 'Failed to reset dashboard data.');
            }
          },
        },
      ]
    );
  };

  if (!settings) {
    return (
      <Container>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ThemedText variant="h2">Loading...</ThemedText>
        </View>
      </Container>
    );
  }

  const currencySymbol = settings.currencyType === 'Custom'
    ? settings.customCurrencyName || 'CUR'
    : settings.currencyType;
  const unitName = settings.unitType === 'Custom'
    ? settings.customUnitName || 'Units'
    : settings.unitType;

  return (
    <Container>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Settings Menu */}
        <Card>
          <ThemedText variant="h2" style={{ marginBottom: 20, textAlign: 'center' }}>
            Settings
          </ThemedText>

          <GradientButton
            title="General Settings"
            icon="settings"
            onPress={() => setActiveSection(activeSection === 'general' ? null : 'general')}
            style={{ marginBottom: 12 }}
          />

          <GradientButton
            title="Appearance"
            icon="palette"
            onPress={() => setActiveSection(activeSection === 'appearance' ? null : 'appearance')}
            style={{ marginBottom: 12 }}
          />

          <GradientButton
            title="Reset Options"
            icon="restore"
            variant="secondary"
            onPress={() => setActiveSection(activeSection === 'reset' ? null : 'reset')}
          />
        </Card>

        {/* General Settings */}
        {activeSection === 'general' && (
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              General Settings
            </ThemedText>

            {/* Currency Selection */}
            <View style={{ marginBottom: 16 }}>
              <ThemedText variant="caption" color="textSecondary" style={{ marginBottom: 8 }}>
                Currency Type:
              </ThemedText>
              <TouchableOpacity
                onPress={() => setShowCurrencyModal(true)}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 12,
                  borderWidth: 1,
                  borderColor: theme.colors.border,
                  borderRadius: 8,
                  backgroundColor: theme.colors.surface,
                }}
              >
                <ThemedText>{currencySymbol}</ThemedText>
                <Icon name="arrow-drop-down" size={24} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {/* Unit Type Selection */}
            <View style={{ marginBottom: 16 }}>
              <ThemedText variant="caption" color="textSecondary" style={{ marginBottom: 8 }}>
                Unit Type:
              </ThemedText>
              <TouchableOpacity
                onPress={() => setShowUnitModal(true)}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 12,
                  borderWidth: 1,
                  borderColor: theme.colors.border,
                  borderRadius: 8,
                  backgroundColor: theme.colors.surface,
                }}
              >
                <ThemedText>{unitName}</ThemedText>
                <Icon name="arrow-drop-down" size={24} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {/* Unit Cost */}
            <ThemedTextInput
              label={`Unit Cost (${currencySymbol} per ${unitName})`}
              value={tempUnitCost}
              onChangeText={setTempUnitCost}
              onBlur={handleUnitCostUpdate}
              keyboardType="numeric"
              icon="attach-money"
            />

            {/* Threshold Limit */}
            <ThemedTextInput
              label={`Low Units Threshold (${unitName})`}
              value={tempThreshold}
              onChangeText={setTempThreshold}
              onBlur={handleThresholdUpdate}
              keyboardType="numeric"
              icon="warning"
            />

            {/* Notification Settings */}
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginVertical: 16 }}>
              <View style={{ flex: 1 }}>
                <ThemedText variant="h3">Daily Notifications</ThemedText>
                <ThemedText variant="caption" color="textSecondary">
                  Remind me to record usage
                </ThemedText>
              </View>
              <Switch
                value={settings.notificationsEnabled}
                onValueChange={(value) => handleUpdateSetting('notificationsEnabled', value)}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                thumbColor={settings.notificationsEnabled ? '#fff' : theme.colors.textSecondary}
              />
            </View>

            {settings.notificationsEnabled && (
              <ThemedTextInput
                label="Notification Time (HH:MM)"
                value={tempNotificationTime}
                onChangeText={setTempNotificationTime}
                onBlur={handleNotificationTimeUpdate}
                placeholder="18:00"
                icon="schedule"
              />
            )}
          </Card>
        )}

        {/* Appearance Settings */}
        {activeSection === 'appearance' && (
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Appearance Settings
            </ThemedText>

            {/* Theme Selection */}
            <View style={{ marginBottom: 16 }}>
              <ThemedText variant="caption" color="textSecondary" style={{ marginBottom: 8 }}>
                Theme:
              </ThemedText>
              <TouchableOpacity
                onPress={() => setShowThemeModal(true)}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 12,
                  borderWidth: 1,
                  borderColor: theme.colors.border,
                  borderRadius: 8,
                  backgroundColor: theme.colors.surface,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View
                    style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      backgroundColor: theme.colors.primary,
                      marginRight: 8,
                    }}
                  />
                  <ThemedText>{theme.name}</ThemedText>
                </View>
                <Icon name="arrow-drop-down" size={24} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {/* Font Selection */}
            <View style={{ marginBottom: 16 }}>
              <ThemedText variant="caption" color="textSecondary" style={{ marginBottom: 8 }}>
                Font Style:
              </ThemedText>
              <TouchableOpacity
                onPress={() => setShowFontModal(true)}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 12,
                  borderWidth: 1,
                  borderColor: theme.colors.border,
                  borderRadius: 8,
                  backgroundColor: theme.colors.surface,
                }}
              >
                <ThemedText>{font.name}</ThemedText>
                <Icon name="arrow-drop-down" size={24} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {/* Preview */}
            <Card style={{ backgroundColor: theme.colors.background }}>
              <ThemedText variant="h3" style={{ marginBottom: 8 }}>
                Preview
              </ThemedText>
              <ThemedText variant="body" style={{ marginBottom: 4 }}>
                This is how your text will look with the current theme and font settings.
              </ThemedText>
              <ThemedText variant="caption" color="textSecondary">
                Current units: 45.2 {unitName} • Cost: {currencySymbol} 6.78
              </ThemedText>
            </Card>
          </Card>
        )}

        {/* Reset Options */}
        {activeSection === 'reset' && (
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Reset Options
            </ThemedText>

            <View style={{ marginBottom: 16 }}>
              <ThemedText variant="h3" style={{ marginBottom: 8 }}>
                Dashboard Data Reset
              </ThemedText>
              <ThemedText variant="body" color="textSecondary" style={{ marginBottom: 12 }}>
                Clears usage records while keeping purchase history and settings intact.
              </ThemedText>
              <GradientButton
                title="Reset Dashboard Data"
                icon="refresh"
                onPress={handleDashboardReset}
                variant="secondary"
              />
            </View>

            <View style={{ height: 1, backgroundColor: theme.colors.border, marginVertical: 16 }} />

            <View>
              <ThemedText variant="h3" style={{ marginBottom: 8 }}>
                Factory Reset
              </ThemedText>
              <ThemedText variant="body" color="error" style={{ marginBottom: 12 }}>
                ⚠️ This will permanently delete ALL data including purchases, usage records, and settings. This action cannot be undone.
              </ThemedText>
              <GradientButton
                title="Factory Reset"
                icon="delete-forever"
                onPress={handleFactoryReset}
                style={{ backgroundColor: theme.colors.error }}
              />
            </View>
          </Card>
        )}

        {/* Modals */}

        {/* Currency Selection Modal */}
        <Modal
          visible={showCurrencyModal}
          transparent
          animationType="slide"
          onRequestClose={() => setShowCurrencyModal(false)}
        >
          <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', padding: 20 }}>
            <Card>
              <ThemedText variant="h3" style={{ marginBottom: 16 }}>
                Select Currency
              </ThemedText>

              {availableCurrencies.map((currency) => (
                <TouchableOpacity
                  key={currency}
                  onPress={() => {
                    if (currency === 'Custom') {
                      // Keep modal open for custom input
                    } else {
                      handleCurrencyChange(currency);
                      setShowCurrencyModal(false);
                    }
                  }}
                  style={{
                    padding: 12,
                    borderBottomWidth: 1,
                    borderBottomColor: theme.colors.border,
                  }}
                >
                  <ThemedText>{currency}</ThemedText>
                </TouchableOpacity>
              ))}

              <ThemedTextInput
                label="Custom Currency Name"
                value={customCurrency}
                onChangeText={setCustomCurrency}
                placeholder="Enter currency name"
                style={{ marginTop: 16 }}
              />

              <View style={{ flexDirection: 'row', marginTop: 16 }}>
                <GradientButton
                  title="Cancel"
                  onPress={() => setShowCurrencyModal(false)}
                  variant="secondary"
                  style={{ flex: 1, marginRight: 8 }}
                />
                <GradientButton
                  title="Save Custom"
                  onPress={handleCustomCurrencySubmit}
                  style={{ flex: 1, marginLeft: 8 }}
                />
              </View>
            </Card>
          </View>
        </Modal>

        {/* Unit Selection Modal */}
        <Modal
          visible={showUnitModal}
          transparent
          animationType="slide"
          onRequestClose={() => setShowUnitModal(false)}
        >
          <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', padding: 20 }}>
            <Card>
              <ThemedText variant="h3" style={{ marginBottom: 16 }}>
                Select Unit Type
              </ThemedText>

              {availableUnits.map((unit) => (
                <TouchableOpacity
                  key={unit}
                  onPress={() => {
                    if (unit === 'Custom') {
                      // Keep modal open for custom input
                    } else {
                      handleUnitChange(unit);
                      setShowUnitModal(false);
                    }
                  }}
                  style={{
                    padding: 12,
                    borderBottomWidth: 1,
                    borderBottomColor: theme.colors.border,
                  }}
                >
                  <ThemedText>{unit}</ThemedText>
                </TouchableOpacity>
              ))}

              <ThemedTextInput
                label="Custom Unit Name"
                value={customUnit}
                onChangeText={setCustomUnit}
                placeholder="Enter unit name"
                style={{ marginTop: 16 }}
              />

              <View style={{ flexDirection: 'row', marginTop: 16 }}>
                <GradientButton
                  title="Cancel"
                  onPress={() => setShowUnitModal(false)}
                  variant="secondary"
                  style={{ flex: 1, marginRight: 8 }}
                />
                <GradientButton
                  title="Save Custom"
                  onPress={handleCustomUnitSubmit}
                  style={{ flex: 1, marginLeft: 8 }}
                />
              </View>
            </Card>
          </View>
        </Modal>

        {/* Theme Selection Modal */}
        <Modal
          visible={showThemeModal}
          transparent
          animationType="slide"
          onRequestClose={() => setShowThemeModal(false)}
        >
          <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', padding: 20 }}>
            <Card>
              <ThemedText variant="h3" style={{ marginBottom: 16 }}>
                Select Theme
              </ThemedText>

              {availableThemes.map((themeName) => {
                const themeData = themes[themeName];
                return (
                  <TouchableOpacity
                    key={themeName}
                    onPress={() => handleThemeChange(themeName)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: 12,
                      borderBottomWidth: 1,
                      borderBottomColor: theme.colors.border,
                    }}
                  >
                    <View
                      style={{
                        width: 20,
                        height: 20,
                        borderRadius: 10,
                        backgroundColor: themeData.colors.primary,
                        marginRight: 12,
                      }}
                    />
                    <ThemedText>{themeData.name}</ThemedText>
                  </TouchableOpacity>
                );
              })}

              <GradientButton
                title="Cancel"
                onPress={() => setShowThemeModal(false)}
                variant="secondary"
                style={{ marginTop: 16 }}
              />
            </Card>
          </View>
        </Modal>

        {/* Font Selection Modal */}
        <Modal
          visible={showFontModal}
          transparent
          animationType="slide"
          onRequestClose={() => setShowFontModal(false)}
        >
          <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', padding: 20 }}>
            <Card>
              <ThemedText variant="h3" style={{ marginBottom: 16 }}>
                Select Font Style
              </ThemedText>

              {availableFonts.map((fontName) => {
                const fontData = fonts[fontName];
                return (
                  <TouchableOpacity
                    key={fontName}
                    onPress={() => handleFontChange(fontName)}
                    style={{
                      padding: 12,
                      borderBottomWidth: 1,
                      borderBottomColor: theme.colors.border,
                    }}
                  >
                    <ThemedText style={{ fontFamily: fontData.fontFamily }}>
                      {fontData.name}
                    </ThemedText>
                  </TouchableOpacity>
                );
              })}

              <GradientButton
                title="Cancel"
                onPress={() => setShowFontModal(false)}
                variant="secondary"
                style={{ marginTop: 16 }}
              />
            </Card>
          </View>
        </Modal>
      </ScrollView>
    </Container>
  );
};

export default SettingsScreen;
