import { getTheme, getFont, themes, fonts } from '../../src/theme/themes';

describe('Theme System', () => {
  describe('getTheme', () => {
    it('should return default theme for unknown theme name', () => {
      const theme = getTheme('unknown');
      expect(theme).toEqual(themes.default);
    });

    it('should return correct theme for valid theme name', () => {
      const theme = getTheme('dark');
      expect(theme).toEqual(themes.dark);
      expect(theme.name).toBe('Dark Mode');
    });

    it('should return blue theme correctly', () => {
      const theme = getTheme('blue');
      expect(theme).toEqual(themes.blue);
      expect(theme.name).toBe('Ocean Blue');
      expect(theme.colors.primary).toBe('#0277BD');
    });

    it('should return green theme correctly', () => {
      const theme = getTheme('green');
      expect(theme).toEqual(themes.green);
      expect(theme.name).toBe('Nature Green');
      expect(theme.colors.primary).toBe('#388E3C');
    });

    it('should return purple theme correctly', () => {
      const theme = getTheme('purple');
      expect(theme).toEqual(themes.purple);
      expect(theme.name).toBe('Royal Purple');
      expect(theme.colors.primary).toBe('#7B1FA2');
    });
  });

  describe('getFont', () => {
    it('should return default font for unknown font name', () => {
      const font = getFont('unknown');
      expect(font).toEqual(fonts.default);
    });

    it('should return correct font for valid font name', () => {
      const font = getFont('roboto');
      expect(font).toEqual(fonts.roboto);
      expect(font.name).toBe('Roboto');
      expect(font.fontFamily).toBe('Roboto');
    });

    it('should return opensans font correctly', () => {
      const font = getFont('opensans');
      expect(font).toEqual(fonts.opensans);
      expect(font.name).toBe('Open Sans');
      expect(font.fontFamily).toBe('OpenSans');
    });
  });

  describe('Theme Structure', () => {
    it('should have all required themes', () => {
      const expectedThemes = ['default', 'dark', 'blue', 'green', 'purple'];
      const actualThemes = Object.keys(themes);
      
      expectedThemes.forEach(themeName => {
        expect(actualThemes).toContain(themeName);
      });
    });

    it('should have all required colors in each theme', () => {
      const requiredColors = [
        'primary',
        'primaryDark',
        'secondary',
        'background',
        'surface',
        'card',
        'text',
        'textSecondary',
        'border',
        'success',
        'warning',
        'error',
        'info',
        'gradient',
      ];

      Object.values(themes).forEach(theme => {
        requiredColors.forEach(colorKey => {
          expect(theme.colors).toHaveProperty(colorKey);
        });
      });
    });

    it('should have gradient as array with two colors', () => {
      Object.values(themes).forEach(theme => {
        expect(Array.isArray(theme.colors.gradient)).toBe(true);
        expect(theme.colors.gradient).toHaveLength(2);
      });
    });
  });

  describe('Font Structure', () => {
    it('should have all required fonts', () => {
      const expectedFonts = ['default', 'roboto', 'opensans', 'lato', 'montserrat'];
      const actualFonts = Object.keys(fonts);
      
      expectedFonts.forEach(fontName => {
        expect(actualFonts).toContain(fontName);
      });
    });

    it('should have all required properties in each font', () => {
      const requiredProperties = ['name', 'fontFamily', 'sizes', 'weights'];
      const requiredSizes = ['small', 'medium', 'large', 'xlarge', 'xxlarge'];
      const requiredWeights = ['light', 'regular', 'medium', 'bold'];

      Object.values(fonts).forEach(font => {
        requiredProperties.forEach(prop => {
          expect(font).toHaveProperty(prop);
        });

        requiredSizes.forEach(size => {
          expect(font.sizes).toHaveProperty(size);
          expect(typeof font.sizes[size as keyof typeof font.sizes]).toBe('number');
        });

        requiredWeights.forEach(weight => {
          expect(font.weights).toHaveProperty(weight);
          expect(typeof font.weights[weight as keyof typeof font.weights]).toBe('string');
        });
      });
    });

    it('should have consistent font weight values', () => {
      Object.values(fonts).forEach(font => {
        expect(['300', '400', '500', '600', '700']).toContain(font.weights.light);
        expect(['300', '400', '500', '600', '700']).toContain(font.weights.regular);
        expect(['300', '400', '500', '600', '700']).toContain(font.weights.medium);
        expect(['300', '400', '500', '600', '700']).toContain(font.weights.bold);
      });
    });
  });

  describe('Default Theme Properties', () => {
    it('should have correct default theme properties', () => {
      const defaultTheme = themes.default;
      
      expect(defaultTheme.name).toBe('Default Blue');
      expect(defaultTheme.colors.primary).toBe('#2196F3');
      expect(defaultTheme.colors.primaryDark).toBe('#1976D2');
      expect(defaultTheme.colors.background).toBe('#F5F5F5');
      expect(defaultTheme.colors.surface).toBe('#FFFFFF');
    });
  });

  describe('Default Font Properties', () => {
    it('should have correct default font properties', () => {
      const defaultFont = fonts.default;
      
      expect(defaultFont.name).toBe('System Default');
      expect(defaultFont.fontFamily).toBe('System');
      expect(defaultFont.sizes.medium).toBe(14);
      expect(defaultFont.weights.regular).toBe('400');
    });
  });
});
