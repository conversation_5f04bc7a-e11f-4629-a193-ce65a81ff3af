import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import DatabaseManager, { Settings } from '../../database/DatabaseManager';

export interface SettingsState {
  settings: Settings | null;
  isLoading: boolean;
  error: string | null;
  availableCurrencies: string[];
  availableUnits: string[];
  availableThemes: string[];
  availableFonts: string[];
}

const initialState: SettingsState = {
  settings: null,
  isLoading: false,
  error: null,
  availableCurrencies: ['USD', 'EUR', 'GBP', 'ZAR', 'NGN', 'Custom'],
  availableUnits: ['Units', 'KWh', 'Custom'],
  availableThemes: ['default', 'dark', 'blue', 'green', 'purple'],
  availableFonts: ['default', 'roboto', 'opensans', 'lato', 'montserrat'],
};

// Async thunks
export const loadSettings = createAsyncThunk(
  'settings/loadSettings',
  async (_, { rejectWithValue }) => {
    try {
      const settings = await DatabaseManager.getSettings();
      return settings;
    } catch (error) {
      return rejectWithValue('Failed to load settings');
    }
  }
);

export const updateSettings = createAsyncThunk(
  'settings/updateSettings',
  async (settingsUpdate: Partial<Settings>, { rejectWithValue }) => {
    try {
      await DatabaseManager.updateSettings(settingsUpdate);
      const updatedSettings = await DatabaseManager.getSettings();
      return updatedSettings;
    } catch (error) {
      return rejectWithValue('Failed to update settings');
    }
  }
);

export const resetSettings = createAsyncThunk(
  'settings/resetSettings',
  async (type: 'factory' | 'dashboard', { rejectWithValue }) => {
    try {
      if (type === 'factory') {
        await DatabaseManager.factoryReset();
      } else {
        await DatabaseManager.dashboardDataReset();
      }
      const settings = await DatabaseManager.getSettings();
      return settings;
    } catch (error) {
      return rejectWithValue('Failed to reset settings');
    }
  }
);

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setSettings: (state, action: PayloadAction<Settings>) => {
      state.settings = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateLocalSettings: (state, action: PayloadAction<Partial<Settings>>) => {
      if (state.settings) {
        state.settings = { ...state.settings, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Load settings
      .addCase(loadSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.settings = action.payload;
      })
      .addCase(loadSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update settings
      .addCase(updateSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.settings = action.payload;
      })
      .addCase(updateSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Reset settings
      .addCase(resetSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.settings = action.payload;
      })
      .addCase(resetSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSettings, clearError, updateLocalSettings } = settingsSlice.actions;

export default settingsSlice.reducer;
