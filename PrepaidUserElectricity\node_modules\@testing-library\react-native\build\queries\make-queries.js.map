{"version": 3, "file": "make-queries.js", "names": ["_errors", "require", "_formatElement", "_logger", "_screen", "_waitFor", "_interopRequireDefault", "e", "__esModule", "default", "deprecatedKeys", "extractDeprecatedWaitForOptions", "options", "undefined", "waitForOptions", "timeout", "interval", "stackTraceError", "for<PERSON>ach", "key", "option", "logger", "warn", "toString", "formatErrorMessage", "message", "printElementTree", "screen", "isDetached", "json", "toJSON", "formatJson", "appendElementTreeToError", "error", "oldMessage", "stack", "replace", "makeQueries", "queryAllByQuery", "getMissingError", "getMultipleError", "getAllByQuery", "instance", "getAllFn", "predicate", "results", "length", "errorMessage", "ErrorWithStack", "query<PERSON><PERSON><PERSON><PERSON><PERSON>", "singleQueryFn", "get<PERSON><PERSON><PERSON><PERSON>y", "getFn", "findAllBy<PERSON><PERSON>y", "findAllFn", "queryOptions", "onTimeout", "deprecatedWaitForOptions", "waitFor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "findFn", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy"], "sources": ["../../src/queries/make-queries.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { ErrorWithStack } from '../helpers/errors';\nimport { formatJson } from '../helpers/format-element';\nimport { logger } from '../helpers/logger';\nimport { screen } from '../screen';\nimport type { WaitForOptions } from '../wait-for';\nimport waitFor from '../wait-for';\n\nexport type GetByQuery<Predicate, Options = void> = (\n  predicate: Predicate,\n  options?: Options,\n) => ReactTestInstance;\n\nexport type GetAllByQuery<Predicate, Options = void> = (\n  predicate: Predicate,\n  options?: Options,\n) => ReactTestInstance[];\n\nexport type QueryByQuery<Predicate, Options = void> = (\n  predicate: Predicate,\n  options?: Options,\n) => ReactTestInstance | null;\n\nexport type QueryAllByQuery<Predicate, Options = void> = (\n  predicate: Predicate,\n  options?: Options,\n) => ReactTestInstance[];\n\nexport type FindByQuery<Predicate, Options = void> = (\n  predicate: Predicate,\n  // Remove `& WaitForOptions` when all queries have been migrated to support 2nd arg query options.\n  options?: Options & WaitForOptions,\n  waitForOptions?: WaitForOptions,\n) => Promise<ReactTestInstance>;\n\nexport type FindAllByQuery<Predicate, Options = void> = (\n  predicate: Predicate,\n  // Remove `& WaitForOptions` when all queries have been migrated to support 2nd arg query options.\n  options?: Options & WaitForOptions,\n  waitForOptions?: WaitForOptions,\n) => Promise<ReactTestInstance[]>;\n\ntype UnboundQuery<Query> = (instance: ReactTestInstance) => Query;\n\nexport type UnboundQueries<Predicate, Options> = {\n  getBy: UnboundQuery<GetByQuery<Predicate, Options>>;\n  getAllBy: UnboundQuery<GetAllByQuery<Predicate, Options>>;\n  queryBy: UnboundQuery<QueryByQuery<Predicate, Options>>;\n  queryAllBy: UnboundQuery<QueryAllByQuery<Predicate, Options>>;\n  findBy: UnboundQuery<FindByQuery<Predicate, Options>>;\n  findAllBy: UnboundQuery<FindAllByQuery<Predicate, Options>>;\n};\n\nconst deprecatedKeys: (keyof WaitForOptions)[] = ['timeout', 'interval', 'stackTraceError'];\n\n// The WaitForOptions has been moved to the second option param of findBy* methods with the adding of TextMatchOptions\n// To make the migration easier and avoid a breaking change, keep reading this options from the first param but warn\nfunction extractDeprecatedWaitForOptions(options?: WaitForOptions) {\n  if (!options) {\n    return undefined;\n  }\n\n  const waitForOptions: WaitForOptions = {\n    timeout: options.timeout,\n    interval: options.interval,\n    stackTraceError: options.stackTraceError,\n  };\n\n  deprecatedKeys.forEach((key) => {\n    const option = options[key];\n    if (option) {\n      logger.warn(\n        `Use of option \"${key}\" in a findBy* query options (2nd parameter) is deprecated. Please pass this option in the waitForOptions (3rd parameter).\nExample:\n\n  findByText(text, {}, { ${key}: ${option.toString()} })`,\n      );\n    }\n  });\n\n  return waitForOptions;\n}\n\nfunction formatErrorMessage(message: string, printElementTree: boolean) {\n  if (!printElementTree) {\n    return message;\n  }\n\n  if (screen.isDetached) {\n    return `${message}\\n\\nScreen is no longer attached. Check your test for \"findBy*\" or \"waitFor\" calls that have not been awaited.\\n\\nWe recommend enabling \"eslint-plugin-testing-library\" to catch these issues at build time:\\nhttps://callstack.github.io/react-native-testing-library/docs/getting-started#eslint-plugin`;\n  }\n\n  const json = screen.toJSON();\n  if (!json) {\n    return message;\n  }\n\n  return `${message}\\n\\n${formatJson(json)}`;\n}\n\nfunction appendElementTreeToError(error: Error) {\n  const oldMessage = error.message;\n  error.message = formatErrorMessage(oldMessage, true);\n\n  // Required to make Jest print the element tree on error\n  error.stack = error.stack?.replace(oldMessage, error.message);\n\n  return error;\n}\n\nexport function makeQueries<Predicate, Options>(\n  queryAllByQuery: UnboundQuery<QueryAllByQuery<Predicate, Options>>,\n  getMissingError: (predicate: Predicate, options?: Options) => string,\n  getMultipleError: (predicate: Predicate, options?: Options) => string,\n): UnboundQueries<Predicate, Options> {\n  function getAllByQuery(instance: ReactTestInstance, { printElementTree = true } = {}) {\n    return function getAllFn(predicate: Predicate, options?: Options) {\n      const results = queryAllByQuery(instance)(predicate, options);\n\n      if (results.length === 0) {\n        const errorMessage = formatErrorMessage(\n          getMissingError(predicate, options),\n          printElementTree,\n        );\n        throw new ErrorWithStack(errorMessage, getAllFn);\n      }\n\n      return results;\n    };\n  }\n\n  function queryByQuery(instance: ReactTestInstance, { printElementTree = true } = {}) {\n    return function singleQueryFn(predicate: Predicate, options?: Options) {\n      const results = queryAllByQuery(instance)(predicate, options);\n\n      if (results.length > 1) {\n        throw new ErrorWithStack(\n          formatErrorMessage(getMultipleError(predicate, options), printElementTree),\n          singleQueryFn,\n        );\n      }\n\n      if (results.length === 0) {\n        return null;\n      }\n\n      return results[0];\n    };\n  }\n\n  function getByQuery(instance: ReactTestInstance, { printElementTree = true } = {}) {\n    return function getFn(predicate: Predicate, options?: Options) {\n      const results = queryAllByQuery(instance)(predicate, options);\n\n      if (results.length > 1) {\n        throw new ErrorWithStack(getMultipleError(predicate, options), getFn);\n      }\n\n      if (results.length === 0) {\n        const errorMessage = formatErrorMessage(\n          getMissingError(predicate, options),\n          printElementTree,\n        );\n        throw new ErrorWithStack(errorMessage, getFn);\n      }\n\n      return results[0];\n    };\n  }\n\n  function findAllByQuery(instance: ReactTestInstance) {\n    return function findAllFn(\n      predicate: Predicate,\n      queryOptions?: Options & WaitForOptions,\n      {\n        onTimeout = (error) => appendElementTreeToError(error),\n        ...waitForOptions\n      }: WaitForOptions = {},\n    ) {\n      const stackTraceError = new ErrorWithStack('STACK_TRACE_ERROR', findAllFn);\n      const deprecatedWaitForOptions = extractDeprecatedWaitForOptions(queryOptions);\n\n      return waitFor(\n        () => getAllByQuery(instance, { printElementTree: false })(predicate, queryOptions),\n        {\n          ...deprecatedWaitForOptions,\n          ...waitForOptions,\n          stackTraceError,\n          onTimeout,\n        },\n      );\n    };\n  }\n\n  function findByQuery(instance: ReactTestInstance) {\n    return function findFn(\n      predicate: Predicate,\n      queryOptions?: Options & WaitForOptions,\n      {\n        onTimeout = (error) => appendElementTreeToError(error),\n        ...waitForOptions\n      }: WaitForOptions = {},\n    ) {\n      const stackTraceError = new ErrorWithStack('STACK_TRACE_ERROR', findFn);\n      const deprecatedWaitForOptions = extractDeprecatedWaitForOptions(queryOptions);\n\n      return waitFor(\n        () => getByQuery(instance, { printElementTree: false })(predicate, queryOptions),\n        {\n          ...deprecatedWaitForOptions,\n          ...waitForOptions,\n          stackTraceError,\n          onTimeout,\n        },\n      );\n    };\n  }\n\n  return {\n    getBy: getByQuery,\n    getAllBy: getAllByQuery,\n    queryBy: queryByQuery,\n    queryAllBy: queryAllByQuery,\n    findBy: findByQuery,\n    findAllBy: findAllByQuery,\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAC,sBAAA,CAAAL,OAAA;AAAkC,SAAAK,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AA+ClC,MAAMG,cAAwC,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC;;AAE3F;AACA;AACA,SAASC,+BAA+BA,CAACC,OAAwB,EAAE;EACjE,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOC,SAAS;EAClB;EAEA,MAAMC,cAA8B,GAAG;IACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO;IACxBC,QAAQ,EAAEJ,OAAO,CAACI,QAAQ;IAC1BC,eAAe,EAAEL,OAAO,CAACK;EAC3B,CAAC;EAEDP,cAAc,CAACQ,OAAO,CAAEC,GAAG,IAAK;IAC9B,MAAMC,MAAM,GAAGR,OAAO,CAACO,GAAG,CAAC;IAC3B,IAAIC,MAAM,EAAE;MACVC,cAAM,CAACC,IAAI,CACT,kBAAkBH,GAAG;AAC7B;AACA;AACA,2BAA2BA,GAAG,KAAKC,MAAM,CAACG,QAAQ,CAAC,CAAC,KAC9C,CAAC;IACH;EACF,CAAC,CAAC;EAEF,OAAOT,cAAc;AACvB;AAEA,SAASU,kBAAkBA,CAACC,OAAe,EAAEC,gBAAyB,EAAE;EACtE,IAAI,CAACA,gBAAgB,EAAE;IACrB,OAAOD,OAAO;EAChB;EAEA,IAAIE,cAAM,CAACC,UAAU,EAAE;IACrB,OAAO,GAAGH,OAAO,2SAA2S;EAC9T;EAEA,MAAMI,IAAI,GAAGF,cAAM,CAACG,MAAM,CAAC,CAAC;EAC5B,IAAI,CAACD,IAAI,EAAE;IACT,OAAOJ,OAAO;EAChB;EAEA,OAAO,GAAGA,OAAO,OAAO,IAAAM,yBAAU,EAACF,IAAI,CAAC,EAAE;AAC5C;AAEA,SAASG,wBAAwBA,CAACC,KAAY,EAAE;EAC9C,MAAMC,UAAU,GAAGD,KAAK,CAACR,OAAO;EAChCQ,KAAK,CAACR,OAAO,GAAGD,kBAAkB,CAACU,UAAU,EAAE,IAAI,CAAC;;EAEpD;EACAD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,EAAEC,OAAO,CAACF,UAAU,EAAED,KAAK,CAACR,OAAO,CAAC;EAE7D,OAAOQ,KAAK;AACd;AAEO,SAASI,WAAWA,CACzBC,eAAkE,EAClEC,eAAoE,EACpEC,gBAAqE,EACjC;EACpC,SAASC,aAAaA,CAACC,QAA2B,EAAE;IAAEhB,gBAAgB,GAAG;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IACpF,OAAO,SAASiB,QAAQA,CAACC,SAAoB,EAAEhC,OAAiB,EAAE;MAChE,MAAMiC,OAAO,GAAGP,eAAe,CAACI,QAAQ,CAAC,CAACE,SAAS,EAAEhC,OAAO,CAAC;MAE7D,IAAIiC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACxB,MAAMC,YAAY,GAAGvB,kBAAkB,CACrCe,eAAe,CAACK,SAAS,EAAEhC,OAAO,CAAC,EACnCc,gBACF,CAAC;QACD,MAAM,IAAIsB,sBAAc,CAACD,YAAY,EAAEJ,QAAQ,CAAC;MAClD;MAEA,OAAOE,OAAO;IAChB,CAAC;EACH;EAEA,SAASI,YAAYA,CAACP,QAA2B,EAAE;IAAEhB,gBAAgB,GAAG;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IACnF,OAAO,SAASwB,aAAaA,CAACN,SAAoB,EAAEhC,OAAiB,EAAE;MACrE,MAAMiC,OAAO,GAAGP,eAAe,CAACI,QAAQ,CAAC,CAACE,SAAS,EAAEhC,OAAO,CAAC;MAE7D,IAAIiC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACtB,MAAM,IAAIE,sBAAc,CACtBxB,kBAAkB,CAACgB,gBAAgB,CAACI,SAAS,EAAEhC,OAAO,CAAC,EAAEc,gBAAgB,CAAC,EAC1EwB,aACF,CAAC;MACH;MAEA,IAAIL,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO,IAAI;MACb;MAEA,OAAOD,OAAO,CAAC,CAAC,CAAC;IACnB,CAAC;EACH;EAEA,SAASM,UAAUA,CAACT,QAA2B,EAAE;IAAEhB,gBAAgB,GAAG;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IACjF,OAAO,SAAS0B,KAAKA,CAACR,SAAoB,EAAEhC,OAAiB,EAAE;MAC7D,MAAMiC,OAAO,GAAGP,eAAe,CAACI,QAAQ,CAAC,CAACE,SAAS,EAAEhC,OAAO,CAAC;MAE7D,IAAIiC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACtB,MAAM,IAAIE,sBAAc,CAACR,gBAAgB,CAACI,SAAS,EAAEhC,OAAO,CAAC,EAAEwC,KAAK,CAAC;MACvE;MAEA,IAAIP,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACxB,MAAMC,YAAY,GAAGvB,kBAAkB,CACrCe,eAAe,CAACK,SAAS,EAAEhC,OAAO,CAAC,EACnCc,gBACF,CAAC;QACD,MAAM,IAAIsB,sBAAc,CAACD,YAAY,EAAEK,KAAK,CAAC;MAC/C;MAEA,OAAOP,OAAO,CAAC,CAAC,CAAC;IACnB,CAAC;EACH;EAEA,SAASQ,cAAcA,CAACX,QAA2B,EAAE;IACnD,OAAO,SAASY,SAASA,CACvBV,SAAoB,EACpBW,YAAuC,EACvC;MACEC,SAAS,GAAIvB,KAAK,IAAKD,wBAAwB,CAACC,KAAK,CAAC;MACtD,GAAGnB;IACW,CAAC,GAAG,CAAC,CAAC,EACtB;MACA,MAAMG,eAAe,GAAG,IAAI+B,sBAAc,CAAC,mBAAmB,EAAEM,SAAS,CAAC;MAC1E,MAAMG,wBAAwB,GAAG9C,+BAA+B,CAAC4C,YAAY,CAAC;MAE9E,OAAO,IAAAG,gBAAO,EACZ,MAAMjB,aAAa,CAACC,QAAQ,EAAE;QAAEhB,gBAAgB,EAAE;MAAM,CAAC,CAAC,CAACkB,SAAS,EAAEW,YAAY,CAAC,EACnF;QACE,GAAGE,wBAAwB;QAC3B,GAAG3C,cAAc;QACjBG,eAAe;QACfuC;MACF,CACF,CAAC;IACH,CAAC;EACH;EAEA,SAASG,WAAWA,CAACjB,QAA2B,EAAE;IAChD,OAAO,SAASkB,MAAMA,CACpBhB,SAAoB,EACpBW,YAAuC,EACvC;MACEC,SAAS,GAAIvB,KAAK,IAAKD,wBAAwB,CAACC,KAAK,CAAC;MACtD,GAAGnB;IACW,CAAC,GAAG,CAAC,CAAC,EACtB;MACA,MAAMG,eAAe,GAAG,IAAI+B,sBAAc,CAAC,mBAAmB,EAAEY,MAAM,CAAC;MACvE,MAAMH,wBAAwB,GAAG9C,+BAA+B,CAAC4C,YAAY,CAAC;MAE9E,OAAO,IAAAG,gBAAO,EACZ,MAAMP,UAAU,CAACT,QAAQ,EAAE;QAAEhB,gBAAgB,EAAE;MAAM,CAAC,CAAC,CAACkB,SAAS,EAAEW,YAAY,CAAC,EAChF;QACE,GAAGE,wBAAwB;QAC3B,GAAG3C,cAAc;QACjBG,eAAe;QACfuC;MACF,CACF,CAAC;IACH,CAAC;EACH;EAEA,OAAO;IACLK,KAAK,EAAEV,UAAU;IACjBW,QAAQ,EAAErB,aAAa;IACvBsB,OAAO,EAAEd,YAAY;IACrBe,UAAU,EAAE1B,eAAe;IAC3B2B,MAAM,EAAEN,WAAW;IACnBO,SAAS,EAAEb;EACb,CAAC;AACH", "ignoreList": []}