{"version": 3, "file": "to-be-expanded.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_accessibility", "_formatElement", "_utils", "e", "__esModule", "default", "toBeExpanded", "element", "checkHostElement", "pass", "computeAriaExpanded", "message", "matcher", "matcherHint", "isNot", "redent", "formatElement", "join", "toBeCollapsed"], "sources": ["../../src/matchers/to-be-expanded.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { computeAriaExpanded } from '../helpers/accessibility';\nimport { formatElement } from '../helpers/format-element';\nimport { checkHostElement } from './utils';\n\nexport function toBeExpanded(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeExpanded, this);\n\n  return {\n    pass: computeAriaExpanded(element) === true,\n    message: () => {\n      const matcher = matcherHint(`${this.isNot ? '.not' : ''}.toBeExpanded`, 'element', '');\n      return [\n        matcher,\n        '',\n        `Received element is ${this.isNot ? '' : 'not '}expanded:`,\n        redent(formatElement(element), 2),\n      ].join('\\n');\n    },\n  };\n}\n\nexport function toBeCollapsed(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeCollapsed, this);\n\n  return {\n    pass: computeAriaExpanded(element) === false,\n    message: () => {\n      const matcher = matcherHint(`${this.isNot ? '.not' : ''}.toBeCollapsed`, 'element', '');\n      return [\n        matcher,\n        '',\n        `Received element is ${this.isNot ? '' : 'not '}collapsed:`,\n        redent(formatElement(element), 2),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAA2C,SAAAE,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,YAAYA,CAA4BC,OAA0B,EAAE;EAClF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,YAAY,EAAE,IAAI,CAAC;EAE7C,OAAO;IACLG,IAAI,EAAE,IAAAC,kCAAmB,EAACH,OAAO,CAAC,KAAK,IAAI;IAC3CI,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,OAAO,GAAG,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,eAAe,EAAE,SAAS,EAAE,EAAE,CAAC;MACtF,OAAO,CACLF,OAAO,EACP,EAAE,EACF,uBAAuB,IAAI,CAACE,KAAK,GAAG,EAAE,GAAG,MAAM,WAAW,EAC1D,IAAAC,eAAM,EAAC,IAAAC,4BAAa,EAACT,OAAO,CAAC,EAAE,CAAC,CAAC,CAClC,CAACU,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEO,SAASC,aAAaA,CAA4BX,OAA0B,EAAE;EACnF,IAAAC,uBAAgB,EAACD,OAAO,EAAEW,aAAa,EAAE,IAAI,CAAC;EAE9C,OAAO;IACLT,IAAI,EAAE,IAAAC,kCAAmB,EAACH,OAAO,CAAC,KAAK,KAAK;IAC5CI,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,OAAO,GAAG,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,gBAAgB,EAAE,SAAS,EAAE,EAAE,CAAC;MACvF,OAAO,CACLF,OAAO,EACP,EAAE,EACF,uBAAuB,IAAI,CAACE,KAAK,GAAG,EAAE,GAAG,MAAM,YAAY,EAC3D,IAAAC,eAAM,EAAC,IAAAC,4BAAa,EAACT,OAAO,CAAC,EAAE,CAAC,CAAC,CAClC,CAACU,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}