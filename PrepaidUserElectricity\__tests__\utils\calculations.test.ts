// Create utility functions for calculations
export const calculateUsage = (previousUnits: number, currentUnits: number): number => {
  return Math.max(0, previousUnits - currentUnits);
};

export const calculateCost = (usage: number, unitCost: number): number => {
  return usage * unitCost;
};

export const calculateUnitsFromCurrency = (currency: number, unitCost: number): number => {
  return unitCost > 0 ? currency / unitCost : 0;
};

export const formatCurrency = (amount: number, currencyType: string): string => {
  return `${currencyType} ${amount.toFixed(2)}`;
};

export const formatUnits = (units: number, unitType: string): string => {
  return `${units.toFixed(2)} ${unitType}`;
};

export const isLowUnits = (currentUnits: number, threshold: number): boolean => {
  return currentUnits <= threshold;
};

export const calculatePercentage = (current: number, max: number): number => {
  return max > 0 ? Math.min(Math.max((current / max) * 100, 0), 100) : 0;
};

export const validateTimeFormat = (time: string): boolean => {
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};

export const getDateRange = (type: 'weekly' | 'monthly'): { start: Date; end: Date } => {
  const end = new Date();
  const start = new Date();
  
  if (type === 'weekly') {
    start.setDate(end.getDate() - 7);
  } else {
    start.setMonth(end.getMonth() - 1);
  }
  
  return { start, end };
};

// Tests
describe('Calculation Utilities', () => {
  describe('calculateUsage', () => {
    it('should calculate usage correctly for normal case', () => {
      expect(calculateUsage(100, 75)).toBe(25);
    });

    it('should return 0 for negative usage', () => {
      expect(calculateUsage(75, 100)).toBe(0);
    });

    it('should handle zero values', () => {
      expect(calculateUsage(0, 0)).toBe(0);
      expect(calculateUsage(50, 0)).toBe(50);
      expect(calculateUsage(0, 25)).toBe(0);
    });

    it('should handle decimal values', () => {
      expect(calculateUsage(100.5, 75.3)).toBeCloseTo(25.2);
    });
  });

  describe('calculateCost', () => {
    it('should calculate cost correctly', () => {
      expect(calculateCost(25, 0.15)).toBe(3.75);
    });

    it('should handle zero usage', () => {
      expect(calculateCost(0, 0.15)).toBe(0);
    });

    it('should handle zero unit cost', () => {
      expect(calculateCost(25, 0)).toBe(0);
    });

    it('should handle decimal values', () => {
      expect(calculateCost(25.5, 0.15)).toBeCloseTo(3.825);
    });
  });

  describe('calculateUnitsFromCurrency', () => {
    it('should calculate units correctly', () => {
      expect(calculateUnitsFromCurrency(50, 0.15)).toBeCloseTo(333.33, 2);
    });

    it('should handle zero currency', () => {
      expect(calculateUnitsFromCurrency(0, 0.15)).toBe(0);
    });

    it('should handle zero unit cost', () => {
      expect(calculateUnitsFromCurrency(50, 0)).toBe(0);
    });

    it('should handle decimal values', () => {
      expect(calculateUnitsFromCurrency(37.5, 0.125)).toBe(300);
    });
  });

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(50.123, 'USD')).toBe('USD 50.12');
    });

    it('should handle zero amount', () => {
      expect(formatCurrency(0, 'EUR')).toBe('EUR 0.00');
    });

    it('should handle different currency types', () => {
      expect(formatCurrency(25.5, 'GBP')).toBe('GBP 25.50');
      expect(formatCurrency(100, 'ZAR')).toBe('ZAR 100.00');
    });
  });

  describe('formatUnits', () => {
    it('should format units correctly', () => {
      expect(formatUnits(75.567, 'Units')).toBe('75.57 Units');
    });

    it('should handle zero units', () => {
      expect(formatUnits(0, 'KWh')).toBe('0.00 KWh');
    });

    it('should handle different unit types', () => {
      expect(formatUnits(25.1, 'KWh')).toBe('25.10 KWh');
      expect(formatUnits(100, 'Custom')).toBe('100.00 Custom');
    });
  });

  describe('isLowUnits', () => {
    it('should return true when units are below threshold', () => {
      expect(isLowUnits(5, 10)).toBe(true);
    });

    it('should return true when units equal threshold', () => {
      expect(isLowUnits(10, 10)).toBe(true);
    });

    it('should return false when units are above threshold', () => {
      expect(isLowUnits(15, 10)).toBe(false);
    });

    it('should handle zero values', () => {
      expect(isLowUnits(0, 10)).toBe(true);
      expect(isLowUnits(5, 0)).toBe(false);
    });
  });

  describe('calculatePercentage', () => {
    it('should calculate percentage correctly', () => {
      expect(calculatePercentage(75, 100)).toBe(75);
    });

    it('should handle zero current value', () => {
      expect(calculatePercentage(0, 100)).toBe(0);
    });

    it('should handle zero max value', () => {
      expect(calculatePercentage(50, 0)).toBe(0);
    });

    it('should cap at 100%', () => {
      expect(calculatePercentage(150, 100)).toBe(100);
    });

    it('should handle decimal values', () => {
      expect(calculatePercentage(33.33, 100)).toBeCloseTo(33.33);
    });
  });

  describe('validateTimeFormat', () => {
    it('should validate correct time formats', () => {
      expect(validateTimeFormat('18:00')).toBe(true);
      expect(validateTimeFormat('09:30')).toBe(true);
      expect(validateTimeFormat('23:59')).toBe(true);
      expect(validateTimeFormat('00:00')).toBe(true);
    });

    it('should reject invalid time formats', () => {
      expect(validateTimeFormat('25:00')).toBe(false);
      expect(validateTimeFormat('18:60')).toBe(false);
      expect(validateTimeFormat('18')).toBe(false);
      expect(validateTimeFormat('18:0')).toBe(false);
      expect(validateTimeFormat('abc')).toBe(false);
    });
  });

  describe('getDateRange', () => {
    it('should return correct weekly date range', () => {
      const { start, end } = getDateRange('weekly');
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      expect(daysDiff).toBe(7);
    });

    it('should return correct monthly date range', () => {
      const { start, end } = getDateRange('monthly');
      const monthsDiff = end.getMonth() - start.getMonth() + (12 * (end.getFullYear() - start.getFullYear()));
      expect(Math.abs(monthsDiff)).toBe(1);
    });
  });
});
