{"version": 3, "file": "text-input.js", "names": ["_base", "require", "TextInputEventBuilder", "exports", "change", "text", "baseSyntheticEvent", "nativeEvent", "target", "eventCount", "keyPress", "key", "submitEditing", "endEditing", "selectionChange", "start", "end", "selection", "contentSizeChange", "width", "height", "contentSize"], "sources": ["../../../src/user-event/event-builder/text-input.ts"], "sourcesContent": ["import type { Size } from '../../types';\nimport type { TextRange } from '../utils/text-range';\nimport { baseSyntheticEvent } from './base';\n\nexport const TextInputEventBuilder = {\n  /**\n   * Experimental values:\n   * - iOS: `{\"eventCount\": 4, \"target\": 75, \"text\": \"Test\"}`\n   * - Android: `{\"eventCount\": 6, \"target\": 53, \"text\": \"Tes\"}`\n   */\n  change: (text: string) => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: { text, target: 0, eventCount: 0 },\n    };\n  },\n\n  /**\n   * Experimental values:\n   * - iOS: `{\"eventCount\": 3, \"key\": \"a\", \"target\": 75}`\n   * - Android: `{\"key\": \"a\"}`\n   */\n  keyPress: (key: string) => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: { key },\n    };\n  },\n\n  /**\n   * Experimental values:\n   * - iOS: `{\"eventCount\": 4, \"target\": 75, \"text\": \"Test\"}`\n   * - Android: `{\"target\": 53, \"text\": \"Test\"}`\n   */\n  submitEditing: (text: string) => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: { text, target: 0 },\n    };\n  },\n\n  /**\n   * Experimental values:\n   * - iOS: `{\"eventCount\": 4, \"target\": 75, \"text\": \"Test\"}`\n   * - Android: `{\"target\": 53, \"text\": \"Test\"}`\n   */\n  endEditing: (text: string) => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: { text, target: 0 },\n    };\n  },\n\n  /**\n   * Experimental values:\n   * - iOS: `{\"selection\": {\"end\": 4, \"start\": 4}, \"target\": 75}`\n   * - Android: `{\"selection\": {\"end\": 4, \"start\": 4}}`\n   */\n  selectionChange: ({ start, end }: TextRange) => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: { selection: { start, end } },\n    };\n  },\n\n  /**\n   * Experimental values:\n   * - iOS: `{\"contentSize\": {\"height\": 21.666666666666668, \"width\": 11.666666666666666}, \"target\": 75}`\n   * - Android: `{\"contentSize\": {\"height\": 61.45454406738281, \"width\": 352.7272644042969}, \"target\": 53}`\n   */\n  contentSizeChange: ({ width, height }: Size) => {\n    return {\n      ...baseSyntheticEvent(),\n      nativeEvent: { contentSize: { width, height }, target: 0 },\n    };\n  },\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,KAAA,GAAAC,OAAA;AAEO,MAAMC,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,GAAG;EACnC;AACF;AACA;AACA;AACA;EACEE,MAAM,EAAGC,IAAY,IAAK;IACxB,OAAO;MACL,GAAG,IAAAC,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QAAEF,IAAI;QAAEG,MAAM,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE;IAChD,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,QAAQ,EAAGC,GAAW,IAAK;IACzB,OAAO;MACL,GAAG,IAAAL,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QAAEI;MAAI;IACrB,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,aAAa,EAAGP,IAAY,IAAK;IAC/B,OAAO;MACL,GAAG,IAAAC,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QAAEF,IAAI;QAAEG,MAAM,EAAE;MAAE;IACjC,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEK,UAAU,EAAGR,IAAY,IAAK;IAC5B,OAAO;MACL,GAAG,IAAAC,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QAAEF,IAAI;QAAEG,MAAM,EAAE;MAAE;IACjC,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEM,eAAe,EAAEA,CAAC;IAAEC,KAAK;IAAEC;EAAe,CAAC,KAAK;IAC9C,OAAO;MACL,GAAG,IAAAV,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QAAEU,SAAS,EAAE;UAAEF,KAAK;UAAEC;QAAI;MAAE;IAC3C,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,iBAAiB,EAAEA,CAAC;IAAEC,KAAK;IAAEC;EAAa,CAAC,KAAK;IAC9C,OAAO;MACL,GAAG,IAAAd,wBAAkB,EAAC,CAAC;MACvBC,WAAW,EAAE;QAAEc,WAAW,EAAE;UAAEF,KAAK;UAAEC;QAAO,CAAC;QAAEZ,MAAM,EAAE;MAAE;IAC3D,CAAC;EACH;AACF,CAAC", "ignoreList": []}