import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { loadHistory, setFilter, setTypeFilter } from '../store/slices/historySlice';
import { loadSettings } from '../store/slices/settingsSlice';
import { loadPurchaseTotals } from '../store/slices/purchasesSlice';
import { loadUsageTotals } from '../store/slices/usageSlice';
import {
  Container,
  Card,
  ThemedText,
  GradientButton,
} from '../components/common/StyledComponents';
import { useTheme } from '../theme/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialIcons';

const HistoryScreen = () => {
  const dispatch = useAppDispatch();
  const { theme, font } = useTheme();

  const [refreshing, setRefreshing] = useState(false);

  // Redux state
  const settings = useAppSelector((state) => state.settings.settings);
  const { filteredHistory, filter, typeFilter, isLoading } = useAppSelector((state) => state.history);
  const { weeklyTotal: weeklyPurchases, monthlyTotal: monthlyPurchases } = useAppSelector((state) => state.purchases);
  const { weeklyUsage, monthlyUsage, weeklyUsageCost, monthlyUsageCost } = useAppSelector((state) => state.usage);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        dispatch(loadSettings()),
        dispatch(loadHistory()),
        dispatch(loadPurchaseTotals()),
        dispatch(loadUsageTotals()),
      ]);
    } catch (error) {
      console.error('Failed to load history data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleFilterChange = (newFilter: 'all' | 'weekly' | 'monthly') => {
    dispatch(setFilter(newFilter));
  };

  const handleTypeFilterChange = (newTypeFilter: 'all' | 'purchase' | 'usage') => {
    dispatch(setTypeFilter(newTypeFilter));
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const currencySymbol = settings?.currencyType === 'Custom'
    ? settings?.customCurrencyName || 'CUR'
    : settings?.currencyType || 'USD';
  const unitName = settings?.unitType === 'Custom'
    ? settings?.customUnitName || 'Units'
    : settings?.unitType || 'Units';

  if (!settings) {
    return (
      <Container>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ThemedText variant="h2">Loading...</ThemedText>
        </View>
      </Container>
    );
  }

  return (
    <Container>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Summary Cards */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            {filter === 'weekly' ? 'Weekly' : filter === 'monthly' ? 'Monthly' : 'Total'} Summary
          </ThemedText>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Icon name="shopping-cart" size={24} color={theme.colors.secondary} />
              <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                Purchases
              </ThemedText>
              <ThemedText variant="h3" color="secondary">
                {currencySymbol} {filter === 'weekly' ? weeklyPurchases.toFixed(2) : filter === 'monthly' ? monthlyPurchases.toFixed(2) : (weeklyPurchases + monthlyPurchases).toFixed(2)}
              </ThemedText>
            </View>

            <View style={{ width: 1, backgroundColor: theme.colors.border, marginHorizontal: 16 }} />

            <View style={{ flex: 1, alignItems: 'center' }}>
              <Icon name="trending-up" size={24} color={theme.colors.primary} />
              <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                Usage
              </ThemedText>
              <ThemedText variant="h3" color="primary">
                {filter === 'weekly' ? weeklyUsage.toFixed(2) : filter === 'monthly' ? monthlyUsage.toFixed(2) : (weeklyUsage + monthlyUsage).toFixed(2)} {unitName}
              </ThemedText>
              <ThemedText variant="caption" color="textSecondary">
                {currencySymbol} {filter === 'weekly' ? weeklyUsageCost.toFixed(2) : filter === 'monthly' ? monthlyUsageCost.toFixed(2) : (weeklyUsageCost + monthlyUsageCost).toFixed(2)}
              </ThemedText>
            </View>
          </View>
        </Card>

        {/* Filter Controls */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            Filter Options
          </ThemedText>

          {/* Time Filter */}
          <ThemedText variant="caption" color="textSecondary" style={{ marginBottom: 8 }}>
            Time Period:
          </ThemedText>
          <View style={{ flexDirection: 'row', marginBottom: 16 }}>
            {(['all', 'weekly', 'monthly'] as const).map((filterOption) => (
              <TouchableOpacity
                key={filterOption}
                onPress={() => handleFilterChange(filterOption)}
                style={{
                  flex: 1,
                  paddingVertical: 8,
                  paddingHorizontal: 12,
                  marginHorizontal: 4,
                  borderRadius: 6,
                  backgroundColor: filter === filterOption ? theme.colors.primary : theme.colors.surface,
                  borderWidth: 1,
                  borderColor: filter === filterOption ? theme.colors.primary : theme.colors.border,
                }}
              >
                <ThemedText
                  variant="caption"
                  style={{
                    textAlign: 'center',
                    color: filter === filterOption ? '#fff' : theme.colors.text,
                  }}
                >
                  {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>

          {/* Type Filter */}
          <ThemedText variant="caption" color="textSecondary" style={{ marginBottom: 8 }}>
            Entry Type:
          </ThemedText>
          <View style={{ flexDirection: 'row' }}>
            {(['all', 'purchase', 'usage'] as const).map((typeOption) => (
              <TouchableOpacity
                key={typeOption}
                onPress={() => handleTypeFilterChange(typeOption)}
                style={{
                  flex: 1,
                  paddingVertical: 8,
                  paddingHorizontal: 12,
                  marginHorizontal: 4,
                  borderRadius: 6,
                  backgroundColor: typeFilter === typeOption ? theme.colors.secondary : theme.colors.surface,
                  borderWidth: 1,
                  borderColor: typeFilter === typeOption ? theme.colors.secondary : theme.colors.border,
                }}
              >
                <ThemedText
                  variant="caption"
                  style={{
                    textAlign: 'center',
                    color: typeFilter === typeOption ? '#fff' : theme.colors.text,
                  }}
                >
                  {typeOption.charAt(0).toUpperCase() + typeOption.slice(1)}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* History Entries */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            History Entries ({filteredHistory.length})
          </ThemedText>

          {filteredHistory.length === 0 ? (
            <View style={{ alignItems: 'center', paddingVertical: 20 }}>
              <Icon name="history" size={48} color={theme.colors.textSecondary} />
              <ThemedText color="textSecondary" style={{ marginTop: 8 }}>
                No entries found
              </ThemedText>
              <ThemedText variant="caption" color="textSecondary">
                Try adjusting your filters
              </ThemedText>
            </View>
          ) : (
            filteredHistory.map((entry, index) => {
              const isPurchase = entry.type === 'purchase';
              const data = entry.data as any;

              return (
                <View
                  key={entry.id || index}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 16,
                    borderBottomWidth: index < filteredHistory.length - 1 ? 1 : 0,
                    borderBottomColor: theme.colors.border,
                  }}
                >
                  {/* Icon */}
                  <View
                    style={{
                      width: 40,
                      height: 40,
                      borderRadius: 20,
                      backgroundColor: isPurchase ? theme.colors.secondary : theme.colors.primary,
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginRight: 12,
                    }}
                  >
                    <Icon
                      name={isPurchase ? 'shopping-cart' : 'trending-up'}
                      size={20}
                      color="#fff"
                    />
                  </View>

                  {/* Content */}
                  <View style={{ flex: 1 }}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                      <ThemedText variant="h3">
                        {isPurchase ? 'Purchase' : 'Usage Record'}
                      </ThemedText>
                      <ThemedText variant="h3" color={isPurchase ? 'secondary' : 'primary'}>
                        {isPurchase
                          ? `${currencySymbol} ${data.currency?.toFixed(2) || '0.00'}`
                          : `${data.usageDifference?.toFixed(2) || '0.00'} ${unitName}`
                        }
                      </ThemedText>
                    </View>

                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 4 }}>
                      <ThemedText variant="caption" color="textSecondary">
                        {formatDate(entry.timestamp)}
                      </ThemedText>
                      <ThemedText variant="caption" color="textSecondary">
                        {isPurchase
                          ? `${data.units?.toFixed(2) || '0.00'} ${unitName} @ ${currencySymbol} ${data.unitCost?.toFixed(3) || '0.000'}`
                          : `${data.previousUnits?.toFixed(1) || '0.0'} → ${data.currentUnits?.toFixed(1) || '0.0'} (${currencySymbol} ${data.cost?.toFixed(2) || '0.00'})`
                        }
                      </ThemedText>
                    </View>
                  </View>
                </View>
              );
            })
          )}
        </Card>

        {/* Export Options */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            Export Options
          </ThemedText>

          <GradientButton
            title="Export Filtered Data"
            icon="file-download"
            onPress={() => {
              // TODO: Implement export functionality
              console.log('Export functionality to be implemented');
            }}
            style={{ marginBottom: 12 }}
          />

          <GradientButton
            title="Share Summary"
            icon="share"
            variant="secondary"
            onPress={() => {
              // TODO: Implement share functionality
              console.log('Share functionality to be implemented');
            }}
          />
        </Card>
      </ScrollView>
    </Container>
  );
};

export default HistoryScreen;
