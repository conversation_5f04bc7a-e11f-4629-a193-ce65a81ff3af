{"logs": [{"outputFile": "com.prepaiduserelectricity.app-mergeDebugResources-26:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\62a6d5f4699f9a58c84c13e5faffb8be\\transformed\\material-1.9.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,353,438,518,603,682,778,894,974,1038,1132,1200,1259,1354,1417,1481,1540,1607,1670,1724,1839,1897,1959,2013,2084,2216,2300,2380,2514,2590,2666,2750,2807,2858,2924,2994,3072,3155,3225,3301,3379,3450,3536,3619,3712,3805,3878,3950,4044,4098,4165,4249,4337,4401,4466,4530,4632,4729,4825,4922,4983,5038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,83,56,50,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,60,54,79", "endOffsets": "260,348,433,513,598,677,773,889,969,1033,1127,1195,1254,1349,1412,1476,1535,1602,1665,1719,1834,1892,1954,2008,2079,2211,2295,2375,2509,2585,2661,2745,2802,2853,2919,2989,3067,3150,3220,3296,3374,3445,3531,3614,3707,3800,3873,3945,4039,4093,4160,4244,4332,4396,4461,4525,4627,4724,4820,4917,4978,5033,5113"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3023,3111,3196,3276,3361,3440,3536,3652,3732,3796,3890,3958,4017,4112,4175,4239,4298,4365,4428,4482,4597,4655,4717,4771,4842,4974,5058,5138,5272,5348,5424,5508,5565,5616,5682,5752,5830,5913,5983,6059,6137,6208,6294,6377,6470,6563,6636,6708,6802,6856,6923,7007,7095,7159,7224,7288,7390,7487,7583,7680,7741,7796", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,83,56,50,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,60,54,79", "endOffsets": "310,3106,3191,3271,3356,3435,3531,3647,3727,3791,3885,3953,4012,4107,4170,4234,4293,4360,4423,4477,4592,4650,4712,4766,4837,4969,5053,5133,5267,5343,5419,5503,5560,5611,5677,5747,5825,5908,5978,6054,6132,6203,6289,6372,6465,6558,6631,6703,6797,6851,6918,7002,7090,7154,7219,7283,7385,7482,7578,7675,7736,7791,7871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\74b37bc7dd1bf2c703c44454fb275d4d\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,425,526,636,724,831,945,1027,1105,1196,1289,1383,1482,1582,1675,1770,1864,1955,2047,2132,2237,2343,2443,2552,2657,2759,2917,7876", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "420,521,631,719,826,940,1022,1100,1191,1284,1378,1477,1577,1670,1765,1859,1950,2042,2127,2232,2338,2438,2547,2652,2754,2912,3018,7955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\32793288452e4e46b72ff0226ea01ae3\\transformed\\core-1.9.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "7960", "endColumns": "100", "endOffsets": "8056"}}]}]}