{"version": 3, "file": "timers.js", "names": ["globalObj", "window", "global", "runWithRealTimers", "callback", "fakeTimersType", "getJestFakeTimersType", "jest", "useRealTimers", "callbackReturnValue", "fakeTimersConfig", "getFakeTimersConfigFromType", "useFakeTimers", "setTimeout", "process", "env", "RNTL_SKIP_AUTO_DETECT_FAKE_TIMERS", "_isMockFunction", "clock", "getRealSystemTime", "type", "legacyFakeTimers", "jestFakeTimersAreEnabled", "Boolean", "exports", "setImmediatePolyfill", "fn", "bindTimeFunctions", "clearTimeoutFn", "clearTimeout", "setImmediateFn", "setImmediate", "setTimeoutFn"], "sources": ["../../src/helpers/timers.ts"], "sourcesContent": ["// Most content of this file sourced directly from https://github.com/testing-library/dom-testing-library/blob/main/src/helpers.js\n/* globals jest */\nconst globalObj = typeof window === 'undefined' ? global : window;\n\ntype FakeTimersTypes = 'modern' | 'legacy';\n\n// Currently this fn only supports jest timers, but it could support other test runners in the future.\nfunction runWithRealTimers<T>(callback: () => T): T {\n  const fakeTimersType = getJestFakeTimersType();\n  if (fakeTimersType) {\n    jest.useRealTimers();\n  }\n\n  const callbackReturnValue = callback();\n\n  if (fakeTimersType) {\n    const fakeTimersConfig = getFakeTimersConfigFromType(fakeTimersType);\n    jest.useFakeTimers(fakeTimersConfig);\n  }\n\n  return callbackReturnValue;\n}\n\nfunction getJestFakeTimersType(): FakeTimersTypes | null {\n  // istanbul ignore if\n  if (\n    typeof jest === 'undefined' ||\n    typeof globalObj.setTimeout === 'undefined' ||\n    process.env.RNTL_SKIP_AUTO_DETECT_FAKE_TIMERS\n  ) {\n    return null;\n  }\n\n  if (\n    // @ts-expect-error jest mutates setTimeout\n    typeof globalObj.setTimeout._isMockFunction !== 'undefined' &&\n    // @ts-expect-error jest mutates setTimeout\n    globalObj.setTimeout._isMockFunction\n  ) {\n    return 'legacy';\n  }\n\n  if (\n    // @ts-expect-error jest mutates setTimeout\n    typeof globalObj.setTimeout.clock !== 'undefined' &&\n    typeof jest.getRealSystemTime !== 'undefined'\n  ) {\n    try {\n      // jest.getRealSystemTime is only supported for Jest's `modern` fake timers and otherwise throws\n      jest.getRealSystemTime();\n      return 'modern';\n    } catch {\n      // not using Jest's modern fake timers\n    }\n  }\n\n  return null;\n}\n\nfunction getFakeTimersConfigFromType(type: FakeTimersTypes) {\n  return type === 'legacy' ? { legacyFakeTimers: true } : { legacyFakeTimers: false };\n}\n\nconst jestFakeTimersAreEnabled = (): boolean => Boolean(getJestFakeTimersType());\n\n// we only run our tests in node, and setImmediate is supported in node.\nfunction setImmediatePolyfill(fn: () => void) {\n  return globalObj.setTimeout(fn, 0);\n}\n\ntype BindTimeFunctions = {\n  clearTimeoutFn: typeof clearTimeout;\n  setImmediateFn: typeof setImmediate;\n  setTimeoutFn: typeof setTimeout;\n};\n\nfunction bindTimeFunctions(): BindTimeFunctions {\n  return {\n    clearTimeoutFn: globalObj.clearTimeout,\n    setImmediateFn: globalObj.setImmediate || setImmediatePolyfill,\n    setTimeoutFn: globalObj.setTimeout,\n  };\n}\n\nconst { clearTimeoutFn, setImmediateFn, setTimeoutFn } = runWithRealTimers(\n  bindTimeFunctions,\n) as BindTimeFunctions;\n\nexport {\n  clearTimeoutFn as clearTimeout,\n  jestFakeTimersAreEnabled,\n  runWithRealTimers,\n  setImmediateFn as setImmediate,\n  setTimeoutFn as setTimeout,\n};\n"], "mappings": ";;;;;;;;AAAA;AACA;AACA,MAAMA,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGC,MAAM,GAAGD,MAAM;AAIjE;AACA,SAASE,iBAAiBA,CAAIC,QAAiB,EAAK;EAClD,MAAMC,cAAc,GAAGC,qBAAqB,CAAC,CAAC;EAC9C,IAAID,cAAc,EAAE;IAClBE,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB;EAEA,MAAMC,mBAAmB,GAAGL,QAAQ,CAAC,CAAC;EAEtC,IAAIC,cAAc,EAAE;IAClB,MAAMK,gBAAgB,GAAGC,2BAA2B,CAACN,cAAc,CAAC;IACpEE,IAAI,CAACK,aAAa,CAACF,gBAAgB,CAAC;EACtC;EAEA,OAAOD,mBAAmB;AAC5B;AAEA,SAASH,qBAAqBA,CAAA,EAA2B;EACvD;EACA,IACE,OAAOC,IAAI,KAAK,WAAW,IAC3B,OAAOP,SAAS,CAACa,UAAU,KAAK,WAAW,IAC3CC,OAAO,CAACC,GAAG,CAACC,iCAAiC,EAC7C;IACA,OAAO,IAAI;EACb;EAEA;EACE;EACA,OAAOhB,SAAS,CAACa,UAAU,CAACI,eAAe,KAAK,WAAW;EAC3D;EACAjB,SAAS,CAACa,UAAU,CAACI,eAAe,EACpC;IACA,OAAO,QAAQ;EACjB;EAEA;EACE;EACA,OAAOjB,SAAS,CAACa,UAAU,CAACK,KAAK,KAAK,WAAW,IACjD,OAAOX,IAAI,CAACY,iBAAiB,KAAK,WAAW,EAC7C;IACA,IAAI;MACF;MACAZ,IAAI,CAACY,iBAAiB,CAAC,CAAC;MACxB,OAAO,QAAQ;IACjB,CAAC,CAAC,MAAM;MACN;IAAA;EAEJ;EAEA,OAAO,IAAI;AACb;AAEA,SAASR,2BAA2BA,CAACS,IAAqB,EAAE;EAC1D,OAAOA,IAAI,KAAK,QAAQ,GAAG;IAAEC,gBAAgB,EAAE;EAAK,CAAC,GAAG;IAAEA,gBAAgB,EAAE;EAAM,CAAC;AACrF;AAEA,MAAMC,wBAAwB,GAAGA,CAAA,KAAeC,OAAO,CAACjB,qBAAqB,CAAC,CAAC,CAAC;;AAEhF;AAAAkB,OAAA,CAAAF,wBAAA,GAAAA,wBAAA;AACA,SAASG,oBAAoBA,CAACC,EAAc,EAAE;EAC5C,OAAO1B,SAAS,CAACa,UAAU,CAACa,EAAE,EAAE,CAAC,CAAC;AACpC;AAQA,SAASC,iBAAiBA,CAAA,EAAsB;EAC9C,OAAO;IACLC,cAAc,EAAE5B,SAAS,CAAC6B,YAAY;IACtCC,cAAc,EAAE9B,SAAS,CAAC+B,YAAY,IAAIN,oBAAoB;IAC9DO,YAAY,EAAEhC,SAAS,CAACa;EAC1B,CAAC;AACH;AAEA,MAAM;EAAEe,cAAc;EAAEE,cAAc;EAAEE;AAAa,CAAC,GAAG7B,iBAAiB,CACxEwB,iBACF,CAAsB;AAACH,OAAA,CAAAX,UAAA,GAAAmB,YAAA;AAAAR,OAAA,CAAAO,YAAA,GAAAD,cAAA;AAAAN,OAAA,CAAAK,YAAA,GAAAD,cAAA", "ignoreList": []}