{"version": 3, "file": "accessibility.js", "names": ["_reactNative", "require", "_componentTree", "_findAll", "_hostComponentNames", "_textContent", "_textInput", "accessibilityStateKeys", "exports", "accessibilityValueKeys", "isHiddenFromAccessibility", "element", "cache", "current", "isCurrentSubtreeInaccessible", "get", "undefined", "isSubtreeInaccessible", "set", "parent", "isInaccessible", "props", "accessibilityElementsHidden", "importantForAccessibility", "flatStyle", "StyleSheet", "flatten", "style", "display", "hostSiblings", "getHostSiblings", "some", "sibling", "computeAriaModal", "isAccessibilityElement", "isHostImage", "alt", "accessible", "isHostText", "isHostTextInput", "isHostSwitch", "getRole", "explicitRole", "role", "accessibilityRole", "normalizeRole", "accessibilityViewIsModal", "computeAriaLabel", "labelElementId", "accessibilityLabelledBy", "rootElement", "getUnsafeRootElement", "labelElement", "findAll", "node", "isHostElement", "nativeID", "includeHiddenElements", "length", "getTextContent", "<PERSON><PERSON><PERSON><PERSON>", "accessibilityLabel", "computeAriaBusy", "accessibilityState", "busy", "computeAriaChecked", "value", "rolesSupportingCheckedState", "checked", "computeAriaDisabled", "isEditableTextInput", "disabled", "computeAriaExpanded", "expanded", "computeAriaSelected", "selected", "computeAriaValue", "accessibilityValue", "ariaValueMax", "ariaValueMin", "ariaValueNow", "ariaValueText", "max", "min", "now", "text", "computeAccessibleName", "checkbox", "radio", "switch"], "sources": ["../../src/helpers/accessibility.ts"], "sourcesContent": ["import type { AccessibilityRole, AccessibilityState, AccessibilityValue, Role } from 'react-native';\nimport { StyleSheet } from 'react-native';\nimport type { ReactTestInstance } from 'react-test-renderer';\n\nimport { getHostSiblings, getUnsafeRootElement, isHostElement } from './component-tree';\nimport { findAll } from './find-all';\nimport { isHostImage, isHostSwitch, isHostText, isHostTextInput } from './host-component-names';\nimport { getTextContent } from './text-content';\nimport { isEditableTextInput } from './text-input';\n\ntype IsInaccessibleOptions = {\n  cache?: WeakMap<ReactTestInstance, boolean>;\n};\n\nexport const accessibilityStateKeys: (keyof AccessibilityState)[] = [\n  'disabled',\n  'selected',\n  'checked',\n  'busy',\n  'expanded',\n];\n\nexport const accessibilityValueKeys: (keyof AccessibilityValue)[] = ['min', 'max', 'now', 'text'];\n\nexport function isHiddenFromAccessibility(\n  element: ReactTestInstance | null,\n  { cache }: IsInaccessibleOptions = {},\n): boolean {\n  if (element == null) {\n    return true;\n  }\n\n  let current: ReactTestInstance | null = element;\n  while (current) {\n    let isCurrentSubtreeInaccessible = cache?.get(current);\n\n    if (isCurrentSubtreeInaccessible === undefined) {\n      isCurrentSubtreeInaccessible = isSubtreeInaccessible(current);\n      cache?.set(current, isCurrentSubtreeInaccessible);\n    }\n\n    if (isCurrentSubtreeInaccessible) {\n      return true;\n    }\n\n    current = current.parent;\n  }\n\n  return false;\n}\n\n/** RTL-compatibility alias for `isHiddenFromAccessibility` */\nexport const isInaccessible = isHiddenFromAccessibility;\n\nfunction isSubtreeInaccessible(element: ReactTestInstance): boolean {\n  // Null props can happen for React.Fragments\n  if (element.props == null) {\n    return false;\n  }\n\n  // See: https://reactnative.dev/docs/accessibility#aria-hidden\n  if (element.props['aria-hidden']) {\n    return true;\n  }\n\n  // iOS: accessibilityElementsHidden\n  // See: https://reactnative.dev/docs/accessibility#accessibilityelementshidden-ios\n  if (element.props.accessibilityElementsHidden) {\n    return true;\n  }\n\n  // Android: importantForAccessibility\n  // See: https://reactnative.dev/docs/accessibility#importantforaccessibility-android\n  if (element.props.importantForAccessibility === 'no-hide-descendants') {\n    return true;\n  }\n\n  // Note that `opacity: 0` is not treated as inaccessible on iOS\n  const flatStyle = StyleSheet.flatten(element.props.style) ?? {};\n  if (flatStyle.display === 'none') return true;\n\n  // iOS: accessibilityViewIsModal or aria-modal\n  // See: https://reactnative.dev/docs/accessibility#accessibilityviewismodal-ios\n  const hostSiblings = getHostSiblings(element);\n  if (hostSiblings.some((sibling) => computeAriaModal(sibling))) {\n    return true;\n  }\n\n  return false;\n}\n\nexport function isAccessibilityElement(element: ReactTestInstance | null): boolean {\n  if (element == null) {\n    return false;\n  }\n\n  // https://github.com/facebook/react-native/blob/8dabed60f456e76a9e53273b601446f34de41fb5/packages/react-native/Libraries/Image/Image.ios.js#L172\n  if (isHostImage(element) && element.props.alt !== undefined) {\n    return true;\n  }\n\n  if (element.props.accessible !== undefined) {\n    return element.props.accessible;\n  }\n\n  return isHostText(element) || isHostTextInput(element) || isHostSwitch(element);\n}\n\n/**\n * Returns the accessibility role for given element. It will return explicit\n * role from either `role` or `accessibilityRole` props if set.\n *\n * If explicit role is not available, it would try to return default element\n * role:\n * - `text` for `Text` elements\n *\n * In all other cases this functions returns `none`.\n *\n * @param element\n * @returns\n */\nexport function getRole(element: ReactTestInstance): Role | AccessibilityRole {\n  const explicitRole = element.props.role ?? element.props.accessibilityRole;\n  if (explicitRole) {\n    return normalizeRole(explicitRole);\n  }\n\n  if (isHostText(element)) {\n    return 'text';\n  }\n\n  // Note: host Image elements report \"image\" role in screen reader only on Android, but not on iOS.\n  // It's better to require explicit role for Image elements.\n\n  return 'none';\n}\n\n/**\n * There are some duplications between (ARIA) `Role` and `AccessibilityRole` types.\n * Resolve them by using ARIA `Role` type where possible.\n *\n * @param role Role to normalize\n * @returns Normalized role\n */\nexport function normalizeRole(role: string): Role | AccessibilityRole {\n  if (role === 'image') {\n    return 'img';\n  }\n\n  return role as Role | AccessibilityRole;\n}\n\nexport function computeAriaModal(element: ReactTestInstance): boolean | undefined {\n  return element.props['aria-modal'] ?? element.props.accessibilityViewIsModal;\n}\n\nexport function computeAriaLabel(element: ReactTestInstance): string | undefined {\n  const labelElementId = element.props['aria-labelledby'] ?? element.props.accessibilityLabelledBy;\n  if (labelElementId) {\n    const rootElement = getUnsafeRootElement(element);\n    const labelElement = findAll(\n      rootElement,\n      (node) => isHostElement(node) && node.props.nativeID === labelElementId,\n      { includeHiddenElements: true },\n    );\n    if (labelElement.length > 0) {\n      return getTextContent(labelElement[0]);\n    }\n  }\n\n  const explicitLabel = element.props['aria-label'] ?? element.props.accessibilityLabel;\n  if (explicitLabel) {\n    return explicitLabel;\n  }\n\n  //https://github.com/facebook/react-native/blob/8dabed60f456e76a9e53273b601446f34de41fb5/packages/react-native/Libraries/Image/Image.ios.js#L173\n  if (isHostImage(element) && element.props.alt) {\n    return element.props.alt;\n  }\n\n  return undefined;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#busy-state\nexport function computeAriaBusy({ props }: ReactTestInstance): boolean {\n  return props['aria-busy'] ?? props.accessibilityState?.busy ?? false;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#checked-state\nexport function computeAriaChecked(element: ReactTestInstance): AccessibilityState['checked'] {\n  const { props } = element;\n\n  if (isHostSwitch(element)) {\n    return props.value;\n  }\n\n  const role = getRole(element);\n  if (!rolesSupportingCheckedState[role]) {\n    return undefined;\n  }\n\n  return props['aria-checked'] ?? props.accessibilityState?.checked;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#disabled-state\nexport function computeAriaDisabled(element: ReactTestInstance): boolean {\n  if (isHostTextInput(element) && !isEditableTextInput(element)) {\n    return true;\n  }\n\n  const { props } = element;\n  return props['aria-disabled'] ?? props.accessibilityState?.disabled ?? false;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#expanded-state\nexport function computeAriaExpanded({ props }: ReactTestInstance): boolean | undefined {\n  return props['aria-expanded'] ?? props.accessibilityState?.expanded;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#selected-state\nexport function computeAriaSelected({ props }: ReactTestInstance): boolean {\n  return props['aria-selected'] ?? props.accessibilityState?.selected ?? false;\n}\n\nexport function computeAriaValue(element: ReactTestInstance): AccessibilityValue {\n  const {\n    accessibilityValue,\n    'aria-valuemax': ariaValueMax,\n    'aria-valuemin': ariaValueMin,\n    'aria-valuenow': ariaValueNow,\n    'aria-valuetext': ariaValueText,\n  } = element.props;\n\n  return {\n    max: ariaValueMax ?? accessibilityValue?.max,\n    min: ariaValueMin ?? accessibilityValue?.min,\n    now: ariaValueNow ?? accessibilityValue?.now,\n    text: ariaValueText ?? accessibilityValue?.text,\n  };\n}\n\nexport function computeAccessibleName(element: ReactTestInstance): string | undefined {\n  return computeAriaLabel(element) ?? getTextContent(element);\n}\n\ntype RoleSupportMap = Partial<Record<Role | AccessibilityRole, true>>;\n\nexport const rolesSupportingCheckedState: RoleSupportMap = {\n  checkbox: true,\n  radio: true,\n  switch: true,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAGA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAMO,MAAMM,sBAAoD,GAAAC,OAAA,CAAAD,sBAAA,GAAG,CAClE,UAAU,EACV,UAAU,EACV,SAAS,EACT,MAAM,EACN,UAAU,CACX;AAEM,MAAME,sBAAoD,GAAAD,OAAA,CAAAC,sBAAA,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAE1F,SAASC,yBAAyBA,CACvCC,OAAiC,EACjC;EAAEC;AAA6B,CAAC,GAAG,CAAC,CAAC,EAC5B;EACT,IAAID,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIE,OAAiC,GAAGF,OAAO;EAC/C,OAAOE,OAAO,EAAE;IACd,IAAIC,4BAA4B,GAAGF,KAAK,EAAEG,GAAG,CAACF,OAAO,CAAC;IAEtD,IAAIC,4BAA4B,KAAKE,SAAS,EAAE;MAC9CF,4BAA4B,GAAGG,qBAAqB,CAACJ,OAAO,CAAC;MAC7DD,KAAK,EAAEM,GAAG,CAACL,OAAO,EAAEC,4BAA4B,CAAC;IACnD;IAEA,IAAIA,4BAA4B,EAAE;MAChC,OAAO,IAAI;IACb;IAEAD,OAAO,GAAGA,OAAO,CAACM,MAAM;EAC1B;EAEA,OAAO,KAAK;AACd;;AAEA;AACO,MAAMC,cAAc,GAAAZ,OAAA,CAAAY,cAAA,GAAGV,yBAAyB;AAEvD,SAASO,qBAAqBA,CAACN,OAA0B,EAAW;EAClE;EACA,IAAIA,OAAO,CAACU,KAAK,IAAI,IAAI,EAAE;IACzB,OAAO,KAAK;EACd;;EAEA;EACA,IAAIV,OAAO,CAACU,KAAK,CAAC,aAAa,CAAC,EAAE;IAChC,OAAO,IAAI;EACb;;EAEA;EACA;EACA,IAAIV,OAAO,CAACU,KAAK,CAACC,2BAA2B,EAAE;IAC7C,OAAO,IAAI;EACb;;EAEA;EACA;EACA,IAAIX,OAAO,CAACU,KAAK,CAACE,yBAAyB,KAAK,qBAAqB,EAAE;IACrE,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,SAAS,GAAGC,uBAAU,CAACC,OAAO,CAACf,OAAO,CAACU,KAAK,CAACM,KAAK,CAAC,IAAI,CAAC,CAAC;EAC/D,IAAIH,SAAS,CAACI,OAAO,KAAK,MAAM,EAAE,OAAO,IAAI;;EAE7C;EACA;EACA,MAAMC,YAAY,GAAG,IAAAC,8BAAe,EAACnB,OAAO,CAAC;EAC7C,IAAIkB,YAAY,CAACE,IAAI,CAAEC,OAAO,IAAKC,gBAAgB,CAACD,OAAO,CAAC,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAEO,SAASE,sBAAsBA,CAACvB,OAAiC,EAAW;EACjF,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,KAAK;EACd;;EAEA;EACA,IAAI,IAAAwB,+BAAW,EAACxB,OAAO,CAAC,IAAIA,OAAO,CAACU,KAAK,CAACe,GAAG,KAAKpB,SAAS,EAAE;IAC3D,OAAO,IAAI;EACb;EAEA,IAAIL,OAAO,CAACU,KAAK,CAACgB,UAAU,KAAKrB,SAAS,EAAE;IAC1C,OAAOL,OAAO,CAACU,KAAK,CAACgB,UAAU;EACjC;EAEA,OAAO,IAAAC,8BAAU,EAAC3B,OAAO,CAAC,IAAI,IAAA4B,mCAAe,EAAC5B,OAAO,CAAC,IAAI,IAAA6B,gCAAY,EAAC7B,OAAO,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS8B,OAAOA,CAAC9B,OAA0B,EAA4B;EAC5E,MAAM+B,YAAY,GAAG/B,OAAO,CAACU,KAAK,CAACsB,IAAI,IAAIhC,OAAO,CAACU,KAAK,CAACuB,iBAAiB;EAC1E,IAAIF,YAAY,EAAE;IAChB,OAAOG,aAAa,CAACH,YAAY,CAAC;EACpC;EAEA,IAAI,IAAAJ,8BAAU,EAAC3B,OAAO,CAAC,EAAE;IACvB,OAAO,MAAM;EACf;;EAEA;EACA;;EAEA,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkC,aAAaA,CAACF,IAAY,EAA4B;EACpE,IAAIA,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO,KAAK;EACd;EAEA,OAAOA,IAAI;AACb;AAEO,SAASV,gBAAgBA,CAACtB,OAA0B,EAAuB;EAChF,OAAOA,OAAO,CAACU,KAAK,CAAC,YAAY,CAAC,IAAIV,OAAO,CAACU,KAAK,CAACyB,wBAAwB;AAC9E;AAEO,SAASC,gBAAgBA,CAACpC,OAA0B,EAAsB;EAC/E,MAAMqC,cAAc,GAAGrC,OAAO,CAACU,KAAK,CAAC,iBAAiB,CAAC,IAAIV,OAAO,CAACU,KAAK,CAAC4B,uBAAuB;EAChG,IAAID,cAAc,EAAE;IAClB,MAAME,WAAW,GAAG,IAAAC,mCAAoB,EAACxC,OAAO,CAAC;IACjD,MAAMyC,YAAY,GAAG,IAAAC,gBAAO,EAC1BH,WAAW,EACVI,IAAI,IAAK,IAAAC,4BAAa,EAACD,IAAI,CAAC,IAAIA,IAAI,CAACjC,KAAK,CAACmC,QAAQ,KAAKR,cAAc,EACvE;MAAES,qBAAqB,EAAE;IAAK,CAChC,CAAC;IACD,IAAIL,YAAY,CAACM,MAAM,GAAG,CAAC,EAAE;MAC3B,OAAO,IAAAC,2BAAc,EAACP,YAAY,CAAC,CAAC,CAAC,CAAC;IACxC;EACF;EAEA,MAAMQ,aAAa,GAAGjD,OAAO,CAACU,KAAK,CAAC,YAAY,CAAC,IAAIV,OAAO,CAACU,KAAK,CAACwC,kBAAkB;EACrF,IAAID,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;;EAEA;EACA,IAAI,IAAAzB,+BAAW,EAACxB,OAAO,CAAC,IAAIA,OAAO,CAACU,KAAK,CAACe,GAAG,EAAE;IAC7C,OAAOzB,OAAO,CAACU,KAAK,CAACe,GAAG;EAC1B;EAEA,OAAOpB,SAAS;AAClB;;AAEA;AACO,SAAS8C,eAAeA,CAAC;EAAEzC;AAAyB,CAAC,EAAW;EACrE,OAAOA,KAAK,CAAC,WAAW,CAAC,IAAIA,KAAK,CAAC0C,kBAAkB,EAAEC,IAAI,IAAI,KAAK;AACtE;;AAEA;AACO,SAASC,kBAAkBA,CAACtD,OAA0B,EAAiC;EAC5F,MAAM;IAAEU;EAAM,CAAC,GAAGV,OAAO;EAEzB,IAAI,IAAA6B,gCAAY,EAAC7B,OAAO,CAAC,EAAE;IACzB,OAAOU,KAAK,CAAC6C,KAAK;EACpB;EAEA,MAAMvB,IAAI,GAAGF,OAAO,CAAC9B,OAAO,CAAC;EAC7B,IAAI,CAACwD,2BAA2B,CAACxB,IAAI,CAAC,EAAE;IACtC,OAAO3B,SAAS;EAClB;EAEA,OAAOK,KAAK,CAAC,cAAc,CAAC,IAAIA,KAAK,CAAC0C,kBAAkB,EAAEK,OAAO;AACnE;;AAEA;AACO,SAASC,mBAAmBA,CAAC1D,OAA0B,EAAW;EACvE,IAAI,IAAA4B,mCAAe,EAAC5B,OAAO,CAAC,IAAI,CAAC,IAAA2D,8BAAmB,EAAC3D,OAAO,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,MAAM;IAAEU;EAAM,CAAC,GAAGV,OAAO;EACzB,OAAOU,KAAK,CAAC,eAAe,CAAC,IAAIA,KAAK,CAAC0C,kBAAkB,EAAEQ,QAAQ,IAAI,KAAK;AAC9E;;AAEA;AACO,SAASC,mBAAmBA,CAAC;EAAEnD;AAAyB,CAAC,EAAuB;EACrF,OAAOA,KAAK,CAAC,eAAe,CAAC,IAAIA,KAAK,CAAC0C,kBAAkB,EAAEU,QAAQ;AACrE;;AAEA;AACO,SAASC,mBAAmBA,CAAC;EAAErD;AAAyB,CAAC,EAAW;EACzE,OAAOA,KAAK,CAAC,eAAe,CAAC,IAAIA,KAAK,CAAC0C,kBAAkB,EAAEY,QAAQ,IAAI,KAAK;AAC9E;AAEO,SAASC,gBAAgBA,CAACjE,OAA0B,EAAsB;EAC/E,MAAM;IACJkE,kBAAkB;IAClB,eAAe,EAAEC,YAAY;IAC7B,eAAe,EAAEC,YAAY;IAC7B,eAAe,EAAEC,YAAY;IAC7B,gBAAgB,EAAEC;EACpB,CAAC,GAAGtE,OAAO,CAACU,KAAK;EAEjB,OAAO;IACL6D,GAAG,EAAEJ,YAAY,IAAID,kBAAkB,EAAEK,GAAG;IAC5CC,GAAG,EAAEJ,YAAY,IAAIF,kBAAkB,EAAEM,GAAG;IAC5CC,GAAG,EAAEJ,YAAY,IAAIH,kBAAkB,EAAEO,GAAG;IAC5CC,IAAI,EAAEJ,aAAa,IAAIJ,kBAAkB,EAAEQ;EAC7C,CAAC;AACH;AAEO,SAASC,qBAAqBA,CAAC3E,OAA0B,EAAsB;EACpF,OAAOoC,gBAAgB,CAACpC,OAAO,CAAC,IAAI,IAAAgD,2BAAc,EAAChD,OAAO,CAAC;AAC7D;AAIO,MAAMwD,2BAA2C,GAAA3D,OAAA,CAAA2D,2BAAA,GAAG;EACzDoB,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE;AACV,CAAC", "ignoreList": []}