
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "rnreanimated.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeReanimatedModuleSpecJSI_installTurboModule(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, BooleanKind, "installTurboModule", "()Z", args, count, cachedMethodId);
}

NativeReanimatedModuleSpecJSI::NativeReanimatedModuleSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["installTurboModule"] = MethodMetadata {0, __hostFunction_NativeReanimatedModuleSpecJSI_installTurboModule};
}
static facebook::jsi::Value __hostFunction_NativeWorkletsModuleSpecJSI_installTurboModule(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, BooleanKind, "installTurboModule", "(Ljava/lang/String;)Z", args, count, cachedMethodId);
}

NativeWorkletsModuleSpecJSI::NativeWorkletsModuleSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["installTurboModule"] = MethodMetadata {1, __hostFunction_NativeWorkletsModuleSpecJSI_installTurboModule};
}

std::shared_ptr<TurboModule> rnreanimated_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "ReanimatedModule") {
    return std::make_shared<NativeReanimatedModuleSpecJSI>(params);
  }
  if (moduleName == "WorkletsModule") {
    return std::make_shared<NativeWorkletsModuleSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
