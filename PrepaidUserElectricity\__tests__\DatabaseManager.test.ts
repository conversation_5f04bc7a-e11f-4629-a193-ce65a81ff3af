import DatabaseManager from '../src/database/DatabaseManager';

// Mock SQLite
jest.mock('react-native-sqlite-storage', () => ({
  DEBUG: jest.fn(),
  enablePromise: jest.fn(),
  openDatabase: jest.fn(() => Promise.resolve({
    executeSql: jest.fn(() => Promise.resolve([{ rows: { length: 0, item: jest.fn() } }])),
    close: jest.fn(() => Promise.resolve()),
  })),
}));

describe('DatabaseManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Database Initialization', () => {
    it('should initialize database successfully', async () => {
      await expect(DatabaseManager.initDatabase()).resolves.not.toThrow();
    });
  });

  describe('Settings Operations', () => {
    it('should get default settings', async () => {
      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve([{
          rows: {
            length: 1,
            item: () => ({
              id: 1,
              unitCost: 0.15,
              thresholdLimit: 10.0,
              currencyType: 'USD',
              unitType: 'Units',
              theme: 'default',
              fontStyle: 'default',
              notificationsEnabled: 1,
              notificationTime: '18:00',
            }),
          },
        }])),
      };

      // Mock the database property
      (DatabaseManager as any).database = mockDatabase;

      const settings = await DatabaseManager.getSettings();
      
      expect(settings).toBeDefined();
      expect(settings.unitCost).toBe(0.15);
      expect(settings.currencyType).toBe('USD');
      expect(settings.notificationsEnabled).toBe(true);
    });

    it('should update settings successfully', async () => {
      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve()),
      };

      (DatabaseManager as any).database = mockDatabase;

      await expect(DatabaseManager.updateSettings({ unitCost: 0.20 })).resolves.not.toThrow();
      expect(mockDatabase.executeSql).toHaveBeenCalled();
    });
  });

  describe('Purchase Operations', () => {
    it('should add purchase successfully', async () => {
      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve([{ insertId: 1 }])),
      };

      (DatabaseManager as any).database = mockDatabase;

      const purchase = {
        currency: 50.0,
        units: 333.33,
        unitCost: 0.15,
        timestamp: new Date().toISOString(),
        currencyType: 'USD',
      };

      const result = await DatabaseManager.addPurchase(purchase);
      
      expect(result).toBe(1);
      expect(mockDatabase.executeSql).toHaveBeenCalledTimes(2); // Insert purchase + add to history
    });

    it('should get purchases successfully', async () => {
      const mockPurchases = [
        {
          id: 1,
          currency: 50.0,
          units: 333.33,
          unitCost: 0.15,
          timestamp: new Date().toISOString(),
          currencyType: 'USD',
        },
      ];

      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve([{
          rows: {
            length: mockPurchases.length,
            item: (index: number) => mockPurchases[index],
          },
        }])),
      };

      (DatabaseManager as any).database = mockDatabase;

      const purchases = await DatabaseManager.getPurchases();
      
      expect(purchases).toHaveLength(1);
      expect(purchases[0].currency).toBe(50.0);
    });
  });

  describe('Usage Record Operations', () => {
    it('should add usage record successfully', async () => {
      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve([{ insertId: 1 }])),
      };

      (DatabaseManager as any).database = mockDatabase;

      const usageRecord = {
        previousUnits: 100.0,
        currentUnits: 75.0,
        usageDifference: 25.0,
        timestamp: new Date().toISOString(),
        cost: 3.75,
      };

      const result = await DatabaseManager.addUsageRecord(usageRecord);
      
      expect(result).toBe(1);
      expect(mockDatabase.executeSql).toHaveBeenCalledTimes(2); // Insert usage + add to history
    });

    it('should get latest usage record', async () => {
      const mockUsageRecord = {
        id: 1,
        previousUnits: 100.0,
        currentUnits: 75.0,
        usageDifference: 25.0,
        timestamp: new Date().toISOString(),
        cost: 3.75,
      };

      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve([{
          rows: {
            length: 1,
            item: () => mockUsageRecord,
          },
        }])),
      };

      (DatabaseManager as any).database = mockDatabase;

      const latestRecord = await DatabaseManager.getLatestUsageRecord();
      
      expect(latestRecord).toBeDefined();
      expect(latestRecord?.usageDifference).toBe(25.0);
    });
  });

  describe('Calculation Methods', () => {
    it('should calculate weekly totals correctly', async () => {
      const mockDatabase = {
        executeSql: jest.fn()
          .mockResolvedValueOnce([{ rows: { item: () => ({ total: 100.0 }) } }])
          .mockResolvedValueOnce([{ rows: { item: () => ({ total: 25.0, totalCost: 3.75 }) } }]),
      };

      (DatabaseManager as any).database = mockDatabase;

      const weeklyTotals = await DatabaseManager.getWeeklyTotals();
      
      expect(weeklyTotals.purchases).toBe(100.0);
      expect(weeklyTotals.usage).toBe(25.0);
      expect(weeklyTotals.cost).toBe(3.75);
    });

    it('should calculate monthly totals correctly', async () => {
      const mockDatabase = {
        executeSql: jest.fn()
          .mockResolvedValueOnce([{ rows: { item: () => ({ total: 400.0 }) } }])
          .mockResolvedValueOnce([{ rows: { item: () => ({ total: 100.0, totalCost: 15.0 }) } }]),
      };

      (DatabaseManager as any).database = mockDatabase;

      const monthlyTotals = await DatabaseManager.getMonthlyTotals();
      
      expect(monthlyTotals.purchases).toBe(400.0);
      expect(monthlyTotals.usage).toBe(100.0);
      expect(monthlyTotals.cost).toBe(15.0);
    });
  });

  describe('Reset Operations', () => {
    it('should perform factory reset successfully', async () => {
      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve()),
      };

      (DatabaseManager as any).database = mockDatabase;

      await expect(DatabaseManager.factoryReset()).resolves.not.toThrow();
      expect(mockDatabase.executeSql).toHaveBeenCalledTimes(5); // 4 deletes + 1 insert default settings
    });

    it('should perform dashboard data reset successfully', async () => {
      const mockDatabase = {
        executeSql: jest.fn(() => Promise.resolve()),
      };

      (DatabaseManager as any).database = mockDatabase;

      await expect(DatabaseManager.dashboardDataReset()).resolves.not.toThrow();
      expect(mockDatabase.executeSql).toHaveBeenCalledTimes(2); // Delete usage records + delete usage history
    });
  });
});
