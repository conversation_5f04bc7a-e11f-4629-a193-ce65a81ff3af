<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e40400af-12be-40af-8f80-a7bffdef68a2" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/.bundle/config" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/.eslintrc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/.prettierrc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/.watchmanconfig" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/App.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/Gemfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/__tests__/App.test.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/build.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/debug.keystore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/proguard-rules.pro" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/debug/AndroidManifest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/java/com/prepaiduserelectricity/MainActivity.kt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/java/com/prepaiduserelectricity/MainApplication.kt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/drawable/rn_edit_text_material.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-hdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-mdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/values/strings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/app/src/main/res/values/styles.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/build.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/gradle.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/gradle/wrapper/gradle-wrapper.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/gradle/wrapper/gradle-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/gradlew" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/gradlew.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/android/settings.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/app.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/babel.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/.xcode.env" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/Podfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity.xcodeproj/project.pbxproj" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity.xcodeproj/xcshareddata/xcschemes/PrepaidUserElectricity.xcscheme" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity/AppDelegate.swift" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity/Images.xcassets/AppIcon.appiconset/Contents.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity/Images.xcassets/Contents.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity/Info.plist" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity/LaunchScreen.storyboard" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/ios/PrepaidUserElectricity/PrivacyInfo.xcprivacy" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/jest.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/metro.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/package-lock.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PrepaidUserElectricity/tsconfig.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/PrepaidUserElectricity" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="2ydyM1jn0ZwtoyOA1sgsl1cOCTu" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e40400af-12be-40af-8f80-a7bffdef68a2" name="Changes" comment="" />
      <created>1750178692571</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750178692571</updated>
    </task>
    <servers />
  </component>
</project>