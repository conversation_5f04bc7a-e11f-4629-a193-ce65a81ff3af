import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface AppState {
  isInitialized: boolean;
  isLoading: boolean;
  currentUnits: number;
  hasInitialValues: boolean;
  showLowUnitsWarning: boolean;
  lastRecordingTimestamp: string | null;
  error: string | null;
}

const initialState: AppState = {
  isInitialized: false,
  isLoading: false,
  currentUnits: 0,
  hasInitialValues: false,
  showLowUnitsWarning: false,
  lastRecordingTimestamp: null,
  error: null,
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.isInitialized = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCurrentUnits: (state, action: PayloadAction<number>) => {
      state.currentUnits = action.payload;
    },
    setHasInitialValues: (state, action: PayloadAction<boolean>) => {
      state.hasInitialValues = action.payload;
    },
    setShowLowUnitsWarning: (state, action: PayloadAction<boolean>) => {
      state.showLowUnitsWarning = action.payload;
    },
    setLastRecordingTimestamp: (state, action: PayloadAction<string | null>) => {
      state.lastRecordingTimestamp = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetApp: (state) => {
      return { ...initialState, isInitialized: true };
    },
  },
});

export const {
  setInitialized,
  setLoading,
  setCurrentUnits,
  setHasInitialValues,
  setShowLowUnitsWarning,
  setLastRecordingTimestamp,
  setError,
  clearError,
  resetApp,
} = appSlice.actions;

export default appSlice.reducer;
