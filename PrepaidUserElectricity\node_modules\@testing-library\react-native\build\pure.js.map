{"version": 3, "file": "pure.js", "names": ["_act", "_interopRequireDefault", "require", "_cleanup", "_fireEvent", "_render", "_waitFor", "_waitForElementToBeRemoved", "_within", "_config", "_accessibility", "_matches", "_renderHook", "_screen", "_userEvent", "e", "__esModule", "default"], "sources": ["../src/pure.ts"], "sourcesContent": ["export { default as act } from './act';\nexport { default as cleanup } from './cleanup';\nexport { default as fireEvent } from './fire-event';\nexport { default as render } from './render';\nexport { default as waitFor } from './wait-for';\nexport { default as waitForElementToBeRemoved } from './wait-for-element-to-be-removed';\nexport { within, getQueriesForElement } from './within';\n\nexport { configure, resetToDefaults } from './config';\nexport { isHiddenFromAccessibility, isInaccessible } from './helpers/accessibility';\nexport { getDefaultNormalizer } from './matches';\nexport { renderHook } from './render-hook';\nexport { screen } from './screen';\nexport { userEvent } from './user-event';\n\nexport type {\n  RenderOptions,\n  RenderResult,\n  RenderResult as RenderAPI,\n  DebugFunction,\n} from './render';\nexport type { RenderHookOptions, RenderHookResult } from './render-hook';\nexport type { Config } from './config';\nexport type { UserEventConfig } from './user-event';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,QAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,0BAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,OAAA,GAAAN,OAAA;AAEA,IAAAO,OAAA,GAAAP,OAAA;AACA,IAAAQ,cAAA,GAAAR,OAAA;AACA,IAAAS,QAAA,GAAAT,OAAA;AACA,IAAAU,WAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AACA,IAAAY,UAAA,GAAAZ,OAAA;AAAyC,SAAAD,uBAAAc,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA", "ignoreList": []}