import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import { VictoryChart, VictoryLine, VictoryArea, VictoryAxis, VictoryTheme } from 'victory-native';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { addUsageRecord, loadUsageRecords, loadUsageTotals } from '../store/slices/usageSlice';
import { loadSettings } from '../store/slices/settingsSlice';
import { setCurrentUnits } from '../store/slices/appSlice';
import {
  Container,
  Card,
  ThemedText,
  ThemedTextInput,
  GradientButton,
} from '../components/common/StyledComponents';
import { useTheme } from '../theme/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialIcons';

const UsageScreen = () => {
  const dispatch = useAppDispatch();
  const { theme, font } = useTheme();
  const screenWidth = Dimensions.get('window').width;

  const [currentUnitsInput, setCurrentUnitsInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Redux state
  const settings = useAppSelector((state) => state.settings.settings);
  const { usageRecords, latestRecord, weeklyUsage, monthlyUsage, weeklyUsageCost, monthlyUsageCost } = useAppSelector((state) => state.usage);
  const { currentUnits } = useAppSelector((state) => state.app);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        dispatch(loadSettings()),
        dispatch(loadUsageRecords()),
        dispatch(loadUsageTotals()),
      ]);
    } catch (error) {
      console.error('Failed to load usage data:', error);
    }
  };

  // Calculations
  const currentUnitsValue = parseFloat(currentUnitsInput) || 0;
  const previousUnits = latestRecord?.currentUnits || 0;
  const usageDifference = Math.max(0, previousUnits - currentUnitsValue);
  const unitCost = settings?.unitCost || 0.15;
  const usageCost = usageDifference * unitCost;

  const currencySymbol = settings?.currencyType === 'Custom'
    ? settings?.customCurrencyName || 'CUR'
    : settings?.currencyType || 'USD';
  const unitName = settings?.unitType === 'Custom'
    ? settings?.customUnitName || 'Units'
    : settings?.unitType || 'Units';

  const handleRecordUsage = async () => {
    if (!currentUnitsInput || currentUnitsValue < 0) {
      Alert.alert('Invalid Input', 'Please enter a valid current units value.');
      return;
    }

    if (!settings) {
      Alert.alert('Error', 'Settings not loaded. Please try again.');
      return;
    }

    if (latestRecord && currentUnitsValue > latestRecord.currentUnits) {
      Alert.alert(
        'Invalid Reading',
        'Current units cannot be higher than the previous reading. Please check your meter reading.'
      );
      return;
    }

    setIsLoading(true);

    try {
      const usageRecord = {
        previousUnits: previousUnits,
        currentUnits: currentUnitsValue,
        usageDifference: usageDifference,
        timestamp: new Date().toISOString(),
        cost: usageCost,
      };

      await dispatch(addUsageRecord(usageRecord));
      await dispatch(setCurrentUnits(currentUnitsValue));
      await dispatch(loadUsageTotals());

      // Clear form
      setCurrentUnitsInput('');

      Alert.alert(
        'Usage Recorded',
        `Successfully recorded usage of ${usageDifference.toFixed(2)} ${unitName} costing ${currencySymbol} ${usageCost.toFixed(2)}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to record usage. Please try again.');
      console.error('Failed to record usage:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Prepare chart data
  const chartData = usageRecords.slice(-10).map((record, index) => ({
    x: index + 1,
    y: record.usageDifference,
    label: new Date(record.timestamp).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
  }));

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!settings) {
    return (
      <Container>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ThemedText variant="h2">Loading...</ThemedText>
        </View>
      </Container>
    );
  }

  return (
    <Container>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Usage Input Section */}
          <Card>
            <ThemedText variant="h2" style={{ marginBottom: 20, textAlign: 'center' }}>
              Record Current Usage
            </ThemedText>

            <ThemedTextInput
              label={`Current Units (${unitName})`}
              value={currentUnitsInput}
              onChangeText={setCurrentUnitsInput}
              keyboardType="numeric"
              placeholder={`Enter current ${unitName} reading`}
              icon="trending-up"
            />

            {/* Usage Calculation Preview */}
            <Card style={{ backgroundColor: theme.colors.background, marginTop: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <Icon name="calculate" size={20} color={theme.colors.primary} />
                <ThemedText variant="h3" style={{ marginLeft: 8 }}>
                  Usage Calculation
                </ThemedText>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                <ThemedText color="textSecondary">Previous Reading:</ThemedText>
                <ThemedText>
                  {previousUnits.toFixed(2)} {unitName}
                </ThemedText>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                <ThemedText color="textSecondary">Current Reading:</ThemedText>
                <ThemedText variant="h3" color="primary">
                  {currentUnitsValue.toFixed(2)} {unitName}
                </ThemedText>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                <ThemedText color="textSecondary">Usage Difference:</ThemedText>
                <ThemedText variant="h3" color="secondary">
                  {usageDifference.toFixed(2)} {unitName}
                </ThemedText>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                <ThemedText color="textSecondary">Unit Cost:</ThemedText>
                <ThemedText>
                  {currencySymbol} {unitCost.toFixed(3)} per {unitName}
                </ThemedText>
              </View>

              <View style={{ height: 1, backgroundColor: theme.colors.border, marginVertical: 8 }} />

              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <ThemedText variant="h3">Total Cost:</ThemedText>
                <ThemedText variant="h3" color="primary">
                  {currencySymbol} {usageCost.toFixed(2)}
                </ThemedText>
              </View>
            </Card>

            <GradientButton
              title={isLoading ? "Recording..." : "Record Usage"}
              icon="save"
              onPress={handleRecordUsage}
              disabled={isLoading || !currentUnitsInput || currentUnitsValue < 0}
              style={{ marginTop: 20 }}
            />
          </Card>

          {/* Usage Chart */}
          {chartData.length > 0 && (
            <Card>
              <ThemedText variant="h3" style={{ marginBottom: 16 }}>
                Usage Trend (Last 10 Records)
              </ThemedText>

              <View style={{ alignItems: 'center' }}>
                <VictoryChart
                  theme={VictoryTheme.material}
                  width={screenWidth - 64}
                  height={200}
                  padding={{ left: 60, top: 20, right: 40, bottom: 40 }}
                >
                  <VictoryAxis dependentAxis />
                  <VictoryAxis />

                  <VictoryArea
                    data={chartData}
                    style={{
                      data: {
                        fill: theme.colors.primary,
                        fillOpacity: 0.3,
                        stroke: theme.colors.primary,
                        strokeWidth: 2,
                      },
                    }}
                    animate={{
                      duration: 1000,
                      onLoad: { duration: 500 },
                    }}
                  />

                  <VictoryLine
                    data={chartData}
                    style={{
                      data: {
                        stroke: theme.colors.primary,
                        strokeWidth: 3,
                      },
                    }}
                    animate={{
                      duration: 1000,
                      onLoad: { duration: 500 },
                    }}
                  />
                </VictoryChart>
              </View>
            </Card>
          )}

          {/* Usage Summary */}
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Usage Summary
            </ThemedText>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Icon name="date-range" size={24} color={theme.colors.primary} />
                <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                  Weekly Usage
                </ThemedText>
                <ThemedText variant="h3" color="primary">
                  {weeklyUsage.toFixed(2)} {unitName}
                </ThemedText>
                <ThemedText variant="caption" color="textSecondary">
                  {currencySymbol} {weeklyUsageCost.toFixed(2)}
                </ThemedText>
              </View>

              <View style={{ width: 1, backgroundColor: theme.colors.border, marginHorizontal: 16 }} />

              <View style={{ flex: 1, alignItems: 'center' }}>
                <Icon name="calendar-today" size={24} color={theme.colors.primary} />
                <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                  Monthly Usage
                </ThemedText>
                <ThemedText variant="h3" color="primary">
                  {monthlyUsage.toFixed(2)} {unitName}
                </ThemedText>
                <ThemedText variant="caption" color="textSecondary">
                  {currencySymbol} {monthlyUsageCost.toFixed(2)}
                </ThemedText>
              </View>
            </View>
          </Card>

          {/* Recent Usage Records */}
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Recent Usage Records
            </ThemedText>

            {usageRecords.length === 0 ? (
              <View style={{ alignItems: 'center', paddingVertical: 20 }}>
                <Icon name="trending-up" size={48} color={theme.colors.textSecondary} />
                <ThemedText color="textSecondary" style={{ marginTop: 8 }}>
                  No usage records yet
                </ThemedText>
                <ThemedText variant="caption" color="textSecondary">
                  Record your first usage above
                </ThemedText>
              </View>
            ) : (
              usageRecords.slice(0, 5).map((record, index) => (
                <View
                  key={record.id || index}
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingVertical: 12,
                    borderBottomWidth: index < Math.min(usageRecords.length, 5) - 1 ? 1 : 0,
                    borderBottomColor: theme.colors.border,
                  }}
                >
                  <View style={{ flex: 1 }}>
                    <ThemedText variant="h3">
                      {record.usageDifference.toFixed(2)} {unitName}
                    </ThemedText>
                    <ThemedText variant="caption" color="textSecondary">
                      {formatDate(record.timestamp)}
                    </ThemedText>
                  </View>

                  <View style={{ alignItems: 'center', marginHorizontal: 16 }}>
                    <ThemedText color="textSecondary">
                      {record.previousUnits.toFixed(1)} → {record.currentUnits.toFixed(1)}
                    </ThemedText>
                  </View>

                  <View style={{ alignItems: 'flex-end' }}>
                    <ThemedText color="primary">
                      {currencySymbol} {record.cost.toFixed(2)}
                    </ThemedText>
                  </View>
                </View>
              ))
            )}

            {usageRecords.length > 5 && (
              <ThemedText
                variant="caption"
                color="primary"
                style={{ textAlign: 'center', marginTop: 12 }}
              >
                View all in History
              </ThemedText>
            )}
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </Container>
  );
};

export default UsageScreen;
