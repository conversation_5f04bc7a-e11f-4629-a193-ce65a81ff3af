{"version": 3, "file": "utils.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_componentTree", "e", "__esModule", "default", "HostElementTypeError", "Error", "constructor", "received", "matcherFn", "context", "captureStackTrace", "withType", "printWithType", "printReceived", "message", "matcherHint", "isNot", "name", "RECEIVED_COLOR", "join", "checkHostElement", "element", "isHostElement", "formatMessage", "matcher", "<PERSON><PERSON><PERSON><PERSON>", "expectedValue", "<PERSON><PERSON><PERSON><PERSON>", "receivedValue", "EXPECTED_COLOR", "redent", "formatValue", "value", "stringify"], "sources": ["../../src/matchers/utils.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport {\n  EXPECTED_COLOR,\n  matcherHint,\n  printReceived,\n  printWithType,\n  RECEIVED_COLOR,\n  stringify,\n} from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { isHostElement } from '../helpers/component-tree';\n\nclass HostElementTypeError extends Error {\n  constructor(received: unknown, matcherFn: jest.CustomMatcher, context: jest.MatcherContext) {\n    super();\n\n    /* istanbul ignore next */\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, matcherFn);\n    }\n\n    let withType = '';\n    try {\n      withType = printWithType('Received', received, printReceived);\n      /* istanbul ignore next */\n    } catch {\n      // Deliberately empty.\n    }\n\n    this.message = [\n      matcherHint(`${context.isNot ? '.not' : ''}.${matcherFn.name}`, 'received', ''),\n      '',\n      `${RECEIVED_COLOR('received')} value must be a host element.`,\n      withType,\n    ].join('\\n');\n  }\n}\n\n/**\n * Throws HostElementTypeError if passed element is not a host element.\n *\n * @param element ReactTestInstance to check.\n * @param matcherFn Matcher function calling the check used for formatting error.\n * @param context Jest matcher context used for formatting error.\n */\nexport function checkHostElement(\n  element: ReactTestInstance | null | undefined,\n  matcherFn: jest.CustomMatcher,\n  context: jest.MatcherContext,\n): asserts element is ReactTestInstance {\n  if (!isHostElement(element)) {\n    throw new HostElementTypeError(element, matcherFn, context);\n  }\n}\n\nexport function formatMessage(\n  matcher: string,\n  expectedLabel: string,\n  expectedValue: string | RegExp | null | undefined,\n  receivedLabel: string,\n  receivedValue: string | null | undefined,\n) {\n  return [\n    `${matcher}\\n`,\n    `${expectedLabel}:\\n${EXPECTED_COLOR(redent(formatValue(expectedValue), 2))}`,\n    `${receivedLabel}:\\n${RECEIVED_COLOR(redent(formatValue(receivedValue), 2))}`,\n  ].join('\\n');\n}\n\nfunction formatValue(value: unknown) {\n  return typeof value === 'string' ? value : stringify(value);\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAQA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AAA0D,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1D,MAAMG,oBAAoB,SAASC,KAAK,CAAC;EACvCC,WAAWA,CAACC,QAAiB,EAAEC,SAA6B,EAAEC,OAA4B,EAAE;IAC1F,KAAK,CAAC,CAAC;;IAEP;IACA,IAAIJ,KAAK,CAACK,iBAAiB,EAAE;MAC3BL,KAAK,CAACK,iBAAiB,CAAC,IAAI,EAAEF,SAAS,CAAC;IAC1C;IAEA,IAAIG,QAAQ,GAAG,EAAE;IACjB,IAAI;MACFA,QAAQ,GAAG,IAAAC,+BAAa,EAAC,UAAU,EAAEL,QAAQ,EAAEM,+BAAa,CAAC;MAC7D;IACF,CAAC,CAAC,MAAM;MACN;IAAA;IAGF,IAAI,CAACC,OAAO,GAAG,CACb,IAAAC,6BAAW,EAAC,GAAGN,OAAO,CAACO,KAAK,GAAG,MAAM,GAAG,EAAE,IAAIR,SAAS,CAACS,IAAI,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC,EAC/E,EAAE,EACF,GAAG,IAAAC,gCAAc,EAAC,UAAU,CAAC,gCAAgC,EAC7DP,QAAQ,CACT,CAACQ,IAAI,CAAC,IAAI,CAAC;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,gBAAgBA,CAC9BC,OAA6C,EAC7Cb,SAA6B,EAC7BC,OAA4B,EACU;EACtC,IAAI,CAAC,IAAAa,4BAAa,EAACD,OAAO,CAAC,EAAE;IAC3B,MAAM,IAAIjB,oBAAoB,CAACiB,OAAO,EAAEb,SAAS,EAAEC,OAAO,CAAC;EAC7D;AACF;AAEO,SAASc,aAAaA,CAC3BC,OAAe,EACfC,aAAqB,EACrBC,aAAiD,EACjDC,aAAqB,EACrBC,aAAwC,EACxC;EACA,OAAO,CACL,GAAGJ,OAAO,IAAI,EACd,GAAGC,aAAa,MAAM,IAAAI,gCAAc,EAAC,IAAAC,eAAM,EAACC,WAAW,CAACL,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAC7E,GAAGC,aAAa,MAAM,IAAAT,gCAAc,EAAC,IAAAY,eAAM,EAACC,WAAW,CAACH,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAC9E,CAACT,IAAI,CAAC,IAAI,CAAC;AACd;AAEA,SAASY,WAAWA,CAACC,KAAc,EAAE;EACnC,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,IAAAC,2BAAS,EAACD,KAAK,CAAC;AAC7D", "ignoreList": []}