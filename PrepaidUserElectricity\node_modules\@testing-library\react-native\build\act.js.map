{"version": 3, "file": "act.js", "names": ["React", "_interopRequireWildcard", "require", "_reactTest<PERSON><PERSON><PERSON>", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "reactAct", "act", "reactTestRendererAct", "setIsReactActEnvironment", "isReactActEnvironment", "globalThis", "IS_REACT_ACT_ENVIRONMENT", "getIsReactActEnvironment", "withGlobalActEnvironment", "actImplementation", "callback", "previousActEnvironment", "callbackNeedsToBeAwaited", "actResult", "result", "then", "thenable", "resolve", "reject", "returnValue", "error", "_default", "exports"], "sources": ["../src/act.ts"], "sourcesContent": ["// This file and the act() implementation is sourced from react-testing-library\n// https://github.com/testing-library/react-testing-library/blob/3dcd8a9649e25054c0e650d95fca2317b7008576/types/index.d.ts\nimport * as React from 'react';\nimport { act as reactTestRendererAct } from 'react-test-renderer';\n\nconst reactAct = typeof React.act === 'function' ? React.act : reactTestRendererAct;\ntype ReactAct = 0 extends 1 & typeof React.act ? typeof reactTestRendererAct : typeof React.act;\n\n// See https://github.com/reactwg/react-18/discussions/102 for more context on global.IS_REACT_ACT_ENVIRONMENT\ndeclare global {\n  // eslint-disable-next-line no-var\n  var IS_REACT_ACT_ENVIRONMENT: boolean | undefined;\n}\n\nfunction setIsReactActEnvironment(isReactActEnvironment: boolean | undefined) {\n  globalThis.IS_REACT_ACT_ENVIRONMENT = isReactActEnvironment;\n}\n\nfunction getIsReactActEnvironment() {\n  return globalThis.IS_REACT_ACT_ENVIRONMENT;\n}\n\nfunction withGlobalActEnvironment(actImplementation: ReactAct) {\n  return (callback: Parameters<ReactAct>[0]) => {\n    const previousActEnvironment = getIsReactActEnvironment();\n    setIsReactActEnvironment(true);\n\n    try {\n      // The return value of `act` is always a thenable.\n      let callbackNeedsToBeAwaited = false;\n      const actResult = actImplementation(() => {\n        const result = callback();\n        // @ts-expect-error TS is too strict here\n        if (result !== null && typeof result === 'object' && typeof result.then === 'function') {\n          callbackNeedsToBeAwaited = true;\n        }\n        return result;\n      });\n\n      if (callbackNeedsToBeAwaited) {\n        const thenable = actResult;\n        return {\n          then: (resolve: (value: never) => never, reject: (value: never) => never) => {\n            // eslint-disable-next-line promise/catch-or-return, promise/prefer-await-to-then\n            thenable.then(\n              // eslint-disable-next-line promise/always-return\n              (returnValue) => {\n                setIsReactActEnvironment(previousActEnvironment);\n                resolve(returnValue as never);\n              },\n              (error) => {\n                setIsReactActEnvironment(previousActEnvironment);\n                reject(error as never);\n              },\n            );\n          },\n        };\n      } else {\n        setIsReactActEnvironment(previousActEnvironment);\n        return actResult;\n      }\n    } catch (error) {\n      // Can't be a `finally {}` block since we don't know if we have to immediately restore IS_REACT_ACT_ENVIRONMENT\n      // or if we have to await the callback first.\n      setIsReactActEnvironment(previousActEnvironment);\n      throw error;\n    }\n  };\n}\n\n// @ts-expect-error: typings get too complex\nconst act = withGlobalActEnvironment(reactAct) as ReactAct;\n\nexport default act;\nexport { getIsReactActEnvironment, setIsReactActEnvironment as setReactActEnvironment };\n"], "mappings": ";;;;;;;;AAEA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAAkE,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAHlE;AACA;;AAIA,MAAMW,QAAQ,GAAG,OAAOxB,KAAK,CAACyB,GAAG,KAAK,UAAU,GAAGzB,KAAK,CAACyB,GAAG,GAAGC,sBAAoB;;AAGnF;;AAMA,SAASC,wBAAwBA,CAACC,qBAA0C,EAAE;EAC5EC,UAAU,CAACC,wBAAwB,GAAGF,qBAAqB;AAC7D;AAEA,SAASG,wBAAwBA,CAAA,EAAG;EAClC,OAAOF,UAAU,CAACC,wBAAwB;AAC5C;AAEA,SAASE,wBAAwBA,CAACC,iBAA2B,EAAE;EAC7D,OAAQC,QAAiC,IAAK;IAC5C,MAAMC,sBAAsB,GAAGJ,wBAAwB,CAAC,CAAC;IACzDJ,wBAAwB,CAAC,IAAI,CAAC;IAE9B,IAAI;MACF;MACA,IAAIS,wBAAwB,GAAG,KAAK;MACpC,MAAMC,SAAS,GAAGJ,iBAAiB,CAAC,MAAM;QACxC,MAAMK,MAAM,GAAGJ,QAAQ,CAAC,CAAC;QACzB;QACA,IAAII,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;UACtFH,wBAAwB,GAAG,IAAI;QACjC;QACA,OAAOE,MAAM;MACf,CAAC,CAAC;MAEF,IAAIF,wBAAwB,EAAE;QAC5B,MAAMI,QAAQ,GAAGH,SAAS;QAC1B,OAAO;UACLE,IAAI,EAAEA,CAACE,OAAgC,EAAEC,MAA+B,KAAK;YAC3E;YACAF,QAAQ,CAACD,IAAI;YACX;YACCI,WAAW,IAAK;cACfhB,wBAAwB,CAACQ,sBAAsB,CAAC;cAChDM,OAAO,CAACE,WAAoB,CAAC;YAC/B,CAAC,EACAC,KAAK,IAAK;cACTjB,wBAAwB,CAACQ,sBAAsB,CAAC;cAChDO,MAAM,CAACE,KAAc,CAAC;YACxB,CACF,CAAC;UACH;QACF,CAAC;MACH,CAAC,MAAM;QACLjB,wBAAwB,CAACQ,sBAAsB,CAAC;QAChD,OAAOE,SAAS;MAClB;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd;MACA;MACAjB,wBAAwB,CAACQ,sBAAsB,CAAC;MAChD,MAAMS,KAAK;IACb;EACF,CAAC;AACH;;AAEA;AACA,MAAMnB,GAAG,GAAGO,wBAAwB,CAACR,QAAQ,CAAa;AAAC,IAAAqB,QAAA,GAAAC,OAAA,CAAApC,OAAA,GAE5Ce,GAAG", "ignoreList": []}