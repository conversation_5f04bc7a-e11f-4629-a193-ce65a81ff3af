{"version": 3, "file": "to-be-disabled.js", "names": ["_jestMatcherU<PERSON>s", "require", "_redent", "_interopRequireDefault", "_accessibility", "_componentTree", "_formatElement", "_utils", "e", "__esModule", "default", "toBeDisabled", "element", "checkHostElement", "isDisabled", "computeAriaDisabled", "isAncestorDisabled", "pass", "message", "is", "isNot", "matcherHint", "redent", "formatElement", "join", "toBeEnabled", "isEnabled", "parent", "getHostParent"], "sources": ["../../src/matchers/to-be-disabled.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport redent from 'redent';\n\nimport { computeAriaDisabled } from '../helpers/accessibility';\nimport { getHostParent } from '../helpers/component-tree';\nimport { formatElement } from '../helpers/format-element';\nimport { checkHostElement } from './utils';\n\nexport function toBeDisabled(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeDisabled, this);\n\n  const isDisabled = computeAriaDisabled(element) || isAncestorDisabled(element);\n\n  return {\n    pass: isDisabled,\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeDisabled`, 'element', ''),\n        '',\n        `Received element ${is} disabled:`,\n        redent(formatElement(element), 2),\n      ].join('\\n');\n    },\n  };\n}\n\nexport function toBeEnabled(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeEnabled, this);\n\n  const isEnabled = !computeAriaDisabled(element) && !isAncestorDisabled(element);\n\n  return {\n    pass: isEnabled,\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeEnabled`, 'element', ''),\n        '',\n        `Received element ${is} enabled:`,\n        redent(formatElement(element), 2),\n      ].join('\\n');\n    },\n  };\n}\n\nfunction isAncestorDisabled(element: ReactTestInstance): boolean {\n  const parent = getHostParent(element);\n  if (parent == null) {\n    return false;\n  }\n\n  return computeAriaDisabled(parent) || isAncestorDisabled(parent);\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AAA2C,SAAAE,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,SAASG,YAAYA,CAA4BC,OAA0B,EAAE;EAClF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,YAAY,EAAE,IAAI,CAAC;EAE7C,MAAMG,UAAU,GAAG,IAAAC,kCAAmB,EAACH,OAAO,CAAC,IAAII,kBAAkB,CAACJ,OAAO,CAAC;EAE9E,OAAO;IACLK,IAAI,EAAEH,UAAU;IAChBI,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,eAAe,EAAE,SAAS,EAAE,EAAE,CAAC,EACtE,EAAE,EACF,oBAAoBD,EAAE,YAAY,EAClC,IAAAG,eAAM,EAAC,IAAAC,4BAAa,EAACX,OAAO,CAAC,EAAE,CAAC,CAAC,CAClC,CAACY,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEO,SAASC,WAAWA,CAA4Bb,OAA0B,EAAE;EACjF,IAAAC,uBAAgB,EAACD,OAAO,EAAEa,WAAW,EAAE,IAAI,CAAC;EAE5C,MAAMC,SAAS,GAAG,CAAC,IAAAX,kCAAmB,EAACH,OAAO,CAAC,IAAI,CAACI,kBAAkB,CAACJ,OAAO,CAAC;EAE/E,OAAO;IACLK,IAAI,EAAES,SAAS;IACfR,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC,EACrE,EAAE,EACF,oBAAoBD,EAAE,WAAW,EACjC,IAAAG,eAAM,EAAC,IAAAC,4BAAa,EAACX,OAAO,CAAC,EAAE,CAAC,CAAC,CAClC,CAACY,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEA,SAASR,kBAAkBA,CAACJ,OAA0B,EAAW;EAC/D,MAAMe,MAAM,GAAG,IAAAC,4BAAa,EAAChB,OAAO,CAAC;EACrC,IAAIe,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,KAAK;EACd;EAEA,OAAO,IAAAZ,kCAAmB,EAACY,MAAM,CAAC,IAAIX,kBAAkB,CAACW,MAAM,CAAC;AAClE", "ignoreList": []}