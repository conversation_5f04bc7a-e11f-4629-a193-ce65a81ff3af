[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\x86\\android_gradle_build.json due to:", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Android\\\\Android Studio\\\\jbr\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging16133782500432861270\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\1c9225614e4d0ab975f35005d007a9db\\\\transformed\\\\react-android-0.80.0-release\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\e3847898c9ad159c30703260241a1547\\\\transformed\\\\hermes-android-0.80.0-release\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\3ba4db1ae352eb90610ca685a00c889a\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\x86'", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\x86'", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\x86\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=80\" ^\n  \"-DANDROID_TOOLCHAIN=clang\" ^\n  \"-DREACT_NATIVE_DIR=C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native\" ^\n  \"-DJS_RUNTIME=hermes\" ^\n  \"-DJS_RUNTIME_DIR=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native\\\\sdks\\\\hermes\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DIS_REANIMATED_EXAMPLE_APP=false\" ^\n  \"-DREANIMATED_VERSION=3.18.0\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\RelWithDebInfo\\\\4r1u6l6j\\\\x86\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=80\" ^\n  \"-DANDROID_TOOLCHAIN=clang\" ^\n  \"-DREACT_NATIVE_DIR=C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native\" ^\n  \"-DJS_RUNTIME=hermes\" ^\n  \"-DJS_RUNTIME_DIR=C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment-Projects\\\\In Production Builds\\\\Prepaid User\\\\PrepaidUserElectricity\\\\node_modules\\\\react-native\\\\sdks\\\\hermes\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DIS_REANIMATED_EXAMPLE_APP=false\" ^\n  \"-DREANIMATED_VERSION=3.18.0\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\x86\\compile_commands.json.bin normally", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\x86\\compile_commands.json to C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\tools\\release\\x86\\compile_commands.json", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]