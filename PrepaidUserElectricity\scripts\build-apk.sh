#!/bin/bash

echo "Building Prepaid User Electricity APK..."
echo

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Check if Android SDK is available
if [ -z "$ANDROID_HOME" ]; then
    echo "Warning: ANDROID_HOME environment variable is not set."
    echo "Please ensure Android SDK is installed and ANDROID_HOME is configured."
    echo
fi

# Clean previous builds
echo "Cleaning previous builds..."
npx react-native clean
if [ $? -ne 0 ]; then
    echo "Warning: Clean command failed, continuing anyway..."
fi

# Install dependencies
echo "Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install dependencies"
    exit 1
fi

# Generate bundle
echo "Generating React Native bundle..."
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/
if [ $? -ne 0 ]; then
    echo "Error: Failed to generate bundle"
    exit 1
fi

# Build debug APK
echo "Building debug APK..."
cd android
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    echo "Error: Failed to build debug APK"
    cd ..
    exit 1
fi

# Build release APK
echo "Building release APK..."
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    echo "Error: Failed to build release APK"
    cd ..
    exit 1
fi

cd ..

echo
echo "Build completed successfully!"
echo
echo "APK files generated:"
echo "- Debug APK: android/app/build/outputs/apk/debug/app-debug.apk"
echo "- Release APK: android/app/build/outputs/apk/release/app-release.apk"
echo
echo "You can install the APK on your Android device or upload to Google Play Store."
echo
