{"name": "@types/react-native-sqlite-storage", "version": "6.0.5", "description": "TypeScript definitions for react-native-sqlite-storage", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-native-sqlite-storage", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "dryganets", "url": "https://github.com/dryganets"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "cavar<PERSON>", "url": "https://github.com/cavarzan"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-native-sqlite-storage"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "fe6d0bc2932e8479dc539ad9f322e1f1ffea728cf675cb93629f062975c41de7", "typeScriptVersion": "4.5"}