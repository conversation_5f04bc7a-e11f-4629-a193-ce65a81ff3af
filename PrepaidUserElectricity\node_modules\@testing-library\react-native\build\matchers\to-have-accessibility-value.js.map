{"version": 3, "file": "to-have-accessibility-value.js", "names": ["_jestMatcherU<PERSON>s", "require", "_accessibility", "_matchAccessibilityValue", "_object", "_utils", "toHaveAccessibilityValue", "element", "expectedValue", "checkHostElement", "receivedValue", "computeAriaValue", "pass", "matchAccessibilityValue", "message", "matcher", "matcherHint", "isNot", "stringify", "formatMessage", "removeUndefinedKeys"], "sources": ["../../src/matchers/to-have-accessibility-value.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, stringify } from 'jest-matcher-utils';\n\nimport { computeAriaValue } from '../helpers/accessibility';\nimport type { AccessibilityValueMatcher } from '../helpers/matchers/match-accessibility-value';\nimport { matchAccessibilityValue } from '../helpers/matchers/match-accessibility-value';\nimport { removeUndefinedKeys } from '../helpers/object';\nimport { checkHostElement, formatMessage } from './utils';\n\nexport function toHaveAccessibilityValue(\n  this: jest.MatcherContext,\n  element: ReactTestInstance,\n  expectedValue: AccessibilityValueMatcher,\n) {\n  checkHostElement(element, toHaveAccessibilityValue, this);\n\n  const receivedValue = computeAriaValue(element);\n\n  return {\n    pass: matchAccessibilityValue(element, expectedValue),\n    message: () => {\n      const matcher = matcherHint(\n        `${this.isNot ? '.not' : ''}.toHaveAccessibilityValue`,\n        'element',\n        stringify(expectedValue),\n      );\n      return formatMessage(\n        matcher,\n        `Expected the element ${this.isNot ? 'not to' : 'to'} have accessibility value`,\n        stringify(expectedValue),\n        'Received element with accessibility value',\n        stringify(removeUndefinedKeys(receivedValue)),\n      );\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAEA,IAAAC,cAAA,GAAAD,OAAA;AAEA,IAAAE,wBAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAEO,SAASK,wBAAwBA,CAEtCC,OAA0B,EAC1BC,aAAwC,EACxC;EACA,IAAAC,uBAAgB,EAACF,OAAO,EAAED,wBAAwB,EAAE,IAAI,CAAC;EAEzD,MAAMI,aAAa,GAAG,IAAAC,+BAAgB,EAACJ,OAAO,CAAC;EAE/C,OAAO;IACLK,IAAI,EAAE,IAAAC,gDAAuB,EAACN,OAAO,EAAEC,aAAa,CAAC;IACrDM,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,OAAO,GAAG,IAAAC,6BAAW,EACzB,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,2BAA2B,EACtD,SAAS,EACT,IAAAC,2BAAS,EAACV,aAAa,CACzB,CAAC;MACD,OAAO,IAAAW,oBAAa,EAClBJ,OAAO,EACP,wBAAwB,IAAI,CAACE,KAAK,GAAG,QAAQ,GAAG,IAAI,2BAA2B,EAC/E,IAAAC,2BAAS,EAACV,aAAa,CAAC,EACxB,2CAA2C,EAC3C,IAAAU,2BAAS,EAAC,IAAAE,2BAAmB,EAACV,aAAa,CAAC,CAC9C,CAAC;IACH;EACF,CAAC;AACH", "ignoreList": []}