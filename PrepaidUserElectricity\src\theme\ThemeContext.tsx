import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { loadSettings } from '../store/slices/settingsSlice';
import { Theme, FontConfig, getTheme, getFont } from './themes';

interface ThemeContextType {
  theme: Theme;
  font: FontConfig;
  isDark: boolean;
  changeTheme: (themeName: string) => void;
  changeFont: (fontName: string) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const settings = useAppSelector((state) => state.settings.settings);
  
  const [theme, setTheme] = useState<Theme>(getTheme('default'));
  const [font, setFont] = useState<FontConfig>(getFont('default'));

  useEffect(() => {
    // Load settings when component mounts
    dispatch(loadSettings());
  }, [dispatch]);

  useEffect(() => {
    if (settings) {
      setTheme(getTheme(settings.theme));
      setFont(getFont(settings.fontStyle));
    }
  }, [settings]);

  const changeTheme = (themeName: string) => {
    setTheme(getTheme(themeName));
  };

  const changeFont = (fontName: string) => {
    setFont(getFont(fontName));
  };

  const isDark = theme.name.toLowerCase().includes('dark');

  const value: ThemeContextType = {
    theme,
    font,
    isDark,
    changeTheme,
    changeFont,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
