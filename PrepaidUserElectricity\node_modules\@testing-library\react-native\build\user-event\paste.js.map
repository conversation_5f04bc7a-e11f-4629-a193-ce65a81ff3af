{"version": 3, "file": "paste.js", "names": ["_errors", "require", "_hostComponentNames", "_pointerEvents", "_textInput", "_nativeState", "_eventBuilder", "_utils", "paste", "element", "text", "isHostTextInput", "ErrorWithStack", "type", "isEditableTextInput", "isPointerEventEnabled", "dispatchEvent", "EventBuilder", "Common", "focus", "textToClear", "getTextInputValue", "rangeToClear", "start", "end", "length", "TextInput", "selectionChange", "nativeState", "valueForElement", "set", "change", "rangeAfter", "isMultiline", "props", "multiline", "contentSize", "getTextContentSize", "contentSizeChange", "wait", "config", "endEditing", "blur"], "sources": ["../../src/user-event/paste.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { ErrorWithStack } from '../helpers/errors';\nimport { isHostTextInput } from '../helpers/host-component-names';\nimport { isPointerEventEnabled } from '../helpers/pointer-events';\nimport { getTextInputValue, isEditableTextInput } from '../helpers/text-input';\nimport { nativeState } from '../native-state';\nimport { EventBuilder } from './event-builder';\nimport type { UserEventInstance } from './setup';\nimport { dispatchEvent, getTextContentSize, wait } from './utils';\n\nexport async function paste(\n  this: UserEventInstance,\n  element: ReactTestInstance,\n  text: string,\n): Promise<void> {\n  if (!isHostTextInput(element)) {\n    throw new ErrorWithStack(\n      `paste() only supports host \"TextInput\" elements. Passed element has type: \"${element.type}\".`,\n      paste,\n    );\n  }\n\n  if (!isEditableTextInput(element) || !isPointerEventEnabled(element)) {\n    return;\n  }\n\n  // 1. Enter element\n  dispatchEvent(element, 'focus', EventBuilder.Common.focus());\n\n  // 2. Select all\n  const textToClear = getTextInputValue(element);\n  const rangeToClear = { start: 0, end: textToClear.length };\n  dispatchEvent(element, 'selectionChange', EventBuilder.TextInput.selectionChange(rangeToClear));\n\n  // 3. Paste the text\n  nativeState.valueForElement.set(element, text);\n  dispatchEvent(element, 'change', EventBuilder.TextInput.change(text));\n  dispatchEvent(element, 'changeText', text);\n\n  const rangeAfter = { start: text.length, end: text.length };\n  dispatchEvent(element, 'selectionChange', EventBuilder.TextInput.selectionChange(rangeAfter));\n\n  // According to the docs only multiline TextInput emits contentSizeChange event\n  // @see: https://reactnative.dev/docs/textinput#oncontentsizechange\n  const isMultiline = element.props.multiline === true;\n  if (isMultiline) {\n    const contentSize = getTextContentSize(text);\n    dispatchEvent(\n      element,\n      'contentSizeChange',\n      EventBuilder.TextInput.contentSizeChange(contentSize),\n    );\n  }\n\n  // 4. Exit element\n  await wait(this.config);\n  dispatchEvent(element, 'endEditing', EventBuilder.TextInput.endEditing(text));\n  dispatchEvent(element, 'blur', EventBuilder.Common.blur());\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AAEA,IAAAM,MAAA,GAAAN,OAAA;AAEO,eAAeO,KAAKA,CAEzBC,OAA0B,EAC1BC,IAAY,EACG;EACf,IAAI,CAAC,IAAAC,mCAAe,EAACF,OAAO,CAAC,EAAE;IAC7B,MAAM,IAAIG,sBAAc,CACtB,8EAA8EH,OAAO,CAACI,IAAI,IAAI,EAC9FL,KACF,CAAC;EACH;EAEA,IAAI,CAAC,IAAAM,8BAAmB,EAACL,OAAO,CAAC,IAAI,CAAC,IAAAM,oCAAqB,EAACN,OAAO,CAAC,EAAE;IACpE;EACF;;EAEA;EACA,IAAAO,oBAAa,EAACP,OAAO,EAAE,OAAO,EAAEQ,0BAAY,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;;EAE5D;EACA,MAAMC,WAAW,GAAG,IAAAC,4BAAiB,EAACZ,OAAO,CAAC;EAC9C,MAAMa,YAAY,GAAG;IAAEC,KAAK,EAAE,CAAC;IAAEC,GAAG,EAAEJ,WAAW,CAACK;EAAO,CAAC;EAC1D,IAAAT,oBAAa,EAACP,OAAO,EAAE,iBAAiB,EAAEQ,0BAAY,CAACS,SAAS,CAACC,eAAe,CAACL,YAAY,CAAC,CAAC;;EAE/F;EACAM,wBAAW,CAACC,eAAe,CAACC,GAAG,CAACrB,OAAO,EAAEC,IAAI,CAAC;EAC9C,IAAAM,oBAAa,EAACP,OAAO,EAAE,QAAQ,EAAEQ,0BAAY,CAACS,SAAS,CAACK,MAAM,CAACrB,IAAI,CAAC,CAAC;EACrE,IAAAM,oBAAa,EAACP,OAAO,EAAE,YAAY,EAAEC,IAAI,CAAC;EAE1C,MAAMsB,UAAU,GAAG;IAAET,KAAK,EAAEb,IAAI,CAACe,MAAM;IAAED,GAAG,EAAEd,IAAI,CAACe;EAAO,CAAC;EAC3D,IAAAT,oBAAa,EAACP,OAAO,EAAE,iBAAiB,EAAEQ,0BAAY,CAACS,SAAS,CAACC,eAAe,CAACK,UAAU,CAAC,CAAC;;EAE7F;EACA;EACA,MAAMC,WAAW,GAAGxB,OAAO,CAACyB,KAAK,CAACC,SAAS,KAAK,IAAI;EACpD,IAAIF,WAAW,EAAE;IACf,MAAMG,WAAW,GAAG,IAAAC,yBAAkB,EAAC3B,IAAI,CAAC;IAC5C,IAAAM,oBAAa,EACXP,OAAO,EACP,mBAAmB,EACnBQ,0BAAY,CAACS,SAAS,CAACY,iBAAiB,CAACF,WAAW,CACtD,CAAC;EACH;;EAEA;EACA,MAAM,IAAAG,WAAI,EAAC,IAAI,CAACC,MAAM,CAAC;EACvB,IAAAxB,oBAAa,EAACP,OAAO,EAAE,YAAY,EAAEQ,0BAAY,CAACS,SAAS,CAACe,UAAU,CAAC/B,IAAI,CAAC,CAAC;EAC7E,IAAAM,oBAAa,EAACP,OAAO,EAAE,MAAM,EAAEQ,0BAAY,CAACC,MAAM,CAACwB,IAAI,CAAC,CAAC,CAAC;AAC5D", "ignoreList": []}