import React, { useState } from 'react';
import {
  View,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAppDispatch } from '../store/hooks';
import { updateSettings } from '../store/slices/settingsSlice';
import { setCurrentUnits, setHasInitialValues } from '../store/slices/appSlice';
import {
  Container,
  Card,
  ThemedText,
  ThemedTextInput,
  GradientButton,
} from '../components/common/StyledComponents';
import { useTheme } from '../theme/ThemeContext';
import { RootStackParamList } from '../navigation/AppNavigator';
import Icon from 'react-native-vector-icons/MaterialIcons';

type InitialSetupNavigationProp = StackNavigationProp<RootStackParamList, 'InitialSetup'>;

const InitialSetupScreen = () => {
  const navigation = useNavigation<InitialSetupNavigationProp>();
  const dispatch = useAppDispatch();
  const { theme, font } = useTheme();

  const [currentUnits, setCurrentUnitsInput] = useState('');
  const [initialCurrency, setInitialCurrency] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleCompleteSetup = async () => {
    const unitsValue = parseFloat(currentUnits);
    const currencyValue = parseFloat(initialCurrency);

    if (!currentUnits || isNaN(unitsValue) || unitsValue < 0) {
      Alert.alert('Invalid Input', 'Please enter a valid current units value.');
      return;
    }

    if (!initialCurrency || isNaN(currencyValue) || currencyValue <= 0) {
      Alert.alert('Invalid Input', 'Please enter a valid initial currency amount.');
      return;
    }

    setIsLoading(true);

    try {
      // Set initial values
      await dispatch(setCurrentUnits(unitsValue));
      await dispatch(setHasInitialValues(true));

      // You could also add an initial purchase record here if needed
      // const initialPurchase = {
      //   currency: currencyValue,
      //   units: unitsValue,
      //   unitCost: 0.15, // Default unit cost
      //   timestamp: new Date().toISOString(),
      //   currencyType: 'USD',
      // };
      // await dispatch(addPurchase(initialPurchase));

      Alert.alert(
        'Setup Complete',
        'Your initial values have been set successfully. You can now start using the app!',
        [
          {
            text: 'Continue',
            onPress: () => navigation.navigate('Main'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to complete setup. Please try again.');
      console.error('Setup error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Welcome Card */}
          <Card style={{ alignItems: 'center', marginVertical: 20 }}>
            <Icon name="flash-on" size={60} color={theme.colors.primary} />
            <ThemedText variant="h1" style={{ marginTop: 16, textAlign: 'center' }}>
              Welcome to
            </ThemedText>
            <ThemedText variant="h2" color="primary" style={{ textAlign: 'center' }}>
              PREPAID USER ELECTRICITY
            </ThemedText>
            <ThemedText variant="body" color="textSecondary" style={{ textAlign: 'center', marginTop: 8 }}>
              Let's set up your account with initial values
            </ThemedText>
          </Card>

          {/* Setup Instructions */}
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Initial Setup Required
            </ThemedText>

            <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 12 }}>
              <Icon name="info" size={20} color={theme.colors.info} style={{ marginRight: 8, marginTop: 2 }} />
              <ThemedText variant="body" color="textSecondary" style={{ flex: 1 }}>
                To get started, we need your current electricity meter reading and any initial credit you may have.
              </ThemedText>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 12 }}>
              <Icon name="trending-up" size={20} color={theme.colors.primary} style={{ marginRight: 8, marginTop: 2 }} />
              <ThemedText variant="body" color="textSecondary" style={{ flex: 1 }}>
                Check your electricity meter and enter the current reading below.
              </ThemedText>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Icon name="attach-money" size={20} color={theme.colors.secondary} style={{ marginRight: 8, marginTop: 2 }} />
              <ThemedText variant="body" color="textSecondary" style={{ flex: 1 }}>
                Enter any existing credit balance you have on your prepaid meter.
              </ThemedText>
            </View>
          </Card>

          {/* Setup Form */}
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 20 }}>
              Enter Initial Values
            </ThemedText>

            <ThemedTextInput
              label="Current Units Reading"
              value={currentUnits}
              onChangeText={setCurrentUnitsInput}
              keyboardType="numeric"
              placeholder="Enter your current meter reading"
              icon="trending-up"
            />

            <ThemedTextInput
              label="Initial Currency Balance (Optional)"
              value={initialCurrency}
              onChangeText={setInitialCurrency}
              keyboardType="numeric"
              placeholder="Enter any existing credit balance"
              icon="attach-money"
            />

            {/* Preview */}
            {currentUnits && (
              <Card style={{ backgroundColor: theme.colors.background, marginTop: 16 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <Icon name="preview" size={20} color={theme.colors.primary} />
                  <ThemedText variant="h3" style={{ marginLeft: 8 }}>
                    Setup Preview
                  </ThemedText>
                </View>

                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                  <ThemedText color="textSecondary">Starting Units:</ThemedText>
                  <ThemedText variant="h3" color="primary">
                    {parseFloat(currentUnits) || 0} Units
                  </ThemedText>
                </View>

                {initialCurrency && (
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <ThemedText color="textSecondary">Initial Balance:</ThemedText>
                    <ThemedText variant="h3" color="secondary">
                      USD {parseFloat(initialCurrency) || 0}
                    </ThemedText>
                  </View>
                )}
              </Card>
            )}

            <GradientButton
              title={isLoading ? "Setting up..." : "Complete Setup"}
              icon="check"
              onPress={handleCompleteSetup}
              disabled={isLoading || !currentUnits}
              style={{ marginTop: 20 }}
            />
          </Card>

          {/* Help Card */}
          <Card>
            <ThemedText variant="h3" style={{ marginBottom: 16 }}>
              Need Help?
            </ThemedText>

            <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 12 }}>
              <Icon name="help" size={20} color={theme.colors.info} style={{ marginRight: 8, marginTop: 2 }} />
              <View style={{ flex: 1 }}>
                <ThemedText variant="body" style={{ marginBottom: 4 }}>
                  Where to find your meter reading:
                </ThemedText>
                <ThemedText variant="caption" color="textSecondary">
                  Look for a digital display on your electricity meter showing the current units consumed.
                </ThemedText>
              </View>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Icon name="account-balance-wallet" size={20} color={theme.colors.secondary} style={{ marginRight: 8, marginTop: 2 }} />
              <View style={{ flex: 1 }}>
                <ThemedText variant="body" style={{ marginBottom: 4 }}>
                  About initial balance:
                </ThemedText>
                <ThemedText variant="caption" color="textSecondary">
                  If you have existing credit on your prepaid meter, enter it here. This is optional and can be skipped.
                </ThemedText>
              </View>
            </View>
          </Card>

          {/* Skip Option */}
          <Card>
            <ThemedText variant="caption" color="textSecondary" style={{ textAlign: 'center', marginBottom: 12 }}>
              You can always update these values later in Settings
            </ThemedText>

            <GradientButton
              title="Skip for Now"
              icon="skip-next"
              variant="secondary"
              onPress={() => {
                Alert.alert(
                  'Skip Setup',
                  'You can set up initial values later in the Settings. Continue to the app?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Continue',
                      onPress: () => navigation.navigate('Main'),
                    },
                  ]
                );
              }}
            />
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </Container>
  );
};

export default InitialSetupScreen;
