{"version": 3, "file": "hint-text.js", "names": ["_findAll", "require", "_matches", "_makeQueries", "getNodeByHintText", "node", "text", "options", "exact", "normalizer", "matches", "props", "accessibilityHint", "queryAllByHintText", "instance", "queryAllByA11yHintFn", "hint", "queryOptions", "findAll", "getMultipleError", "String", "getMissingError", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy", "makeQueries", "bindByHintTextQueries", "getByHintText", "getAllByHintText", "queryByHintText", "findByHintText", "findAllByHintText", "getByA11yHint", "getAllByA11yHint", "queryByA11yHint", "queryAllByA11yHint", "findByA11yHint", "findAllByA11yHint", "getByAccessibilityHint", "getAllByAccessibilityHint", "queryByAccessibilityHint", "queryAllByAccessibilityHint", "findByAccessibilityHint", "findAllByAccessibilityHint", "exports"], "sources": ["../../src/queries/hint-text.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { findAll } from '../helpers/find-all';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport { matches } from '../matches';\nimport type {\n  FindAllByQuery,\n  FindByQuery,\n  GetAllByQuery,\n  GetByQuery,\n  QueryAllByQuery,\n  QueryByQuery,\n} from './make-queries';\nimport { makeQueries } from './make-queries';\nimport type { CommonQueryOptions } from './options';\n\ntype ByHintTextOptions = CommonQueryOptions & TextMatchOptions;\n\nconst getNodeByHintText = (\n  node: ReactTestInstance,\n  text: TextMatch,\n  options: TextMatchOptions = {},\n) => {\n  const { exact, normalizer } = options;\n  return matches(text, node.props.accessibilityHint, normalizer, exact);\n};\n\nconst queryAllByHintText = (\n  instance: ReactTestInstance,\n): QueryAllByQuery<TextMatch, ByHintTextOptions> =>\n  function queryAllByA11yHintFn(hint, queryOptions) {\n    return findAll(instance, (node) => getNodeByHintText(node, hint, queryOptions), queryOptions);\n  };\n\nconst getMultipleError = (hint: TextMatch) =>\n  `Found multiple elements with accessibility hint: ${String(hint)} `;\nconst getMissingError = (hint: TextMatch) =>\n  `Unable to find an element with accessibility hint: ${String(hint)}`;\n\nconst { getBy, getAllBy, queryBy, queryAllBy, findBy, findAllBy } = makeQueries(\n  queryAllByHintText,\n  getMissingError,\n  getMultipleError,\n);\n\nexport type ByHintTextQueries = {\n  getByHintText: GetByQuery<TextMatch, ByHintTextOptions>;\n  getAllByHintText: GetAllByQuery<TextMatch, ByHintTextOptions>;\n  queryByHintText: QueryByQuery<TextMatch, ByHintTextOptions>;\n  queryAllByHintText: QueryAllByQuery<TextMatch, ByHintTextOptions>;\n  findByHintText: FindByQuery<TextMatch, ByHintTextOptions>;\n  findAllByHintText: FindAllByQuery<TextMatch, ByHintTextOptions>;\n\n  // a11yHint aliases\n  getByA11yHint: GetByQuery<TextMatch, ByHintTextOptions>;\n  getAllByA11yHint: GetAllByQuery<TextMatch, ByHintTextOptions>;\n  queryByA11yHint: QueryByQuery<TextMatch, ByHintTextOptions>;\n  queryAllByA11yHint: QueryAllByQuery<TextMatch, ByHintTextOptions>;\n  findByA11yHint: FindByQuery<TextMatch, ByHintTextOptions>;\n  findAllByA11yHint: FindAllByQuery<TextMatch, ByHintTextOptions>;\n\n  // accessibilityHint aliases\n  getByAccessibilityHint: GetByQuery<TextMatch, ByHintTextOptions>;\n  getAllByAccessibilityHint: GetAllByQuery<TextMatch, ByHintTextOptions>;\n  queryByAccessibilityHint: QueryByQuery<TextMatch, ByHintTextOptions>;\n  queryAllByAccessibilityHint: QueryAllByQuery<TextMatch, ByHintTextOptions>;\n  findByAccessibilityHint: FindByQuery<TextMatch, ByHintTextOptions>;\n  findAllByAccessibilityHint: FindAllByQuery<TextMatch, ByHintTextOptions>;\n};\n\nexport const bindByHintTextQueries = (instance: ReactTestInstance): ByHintTextQueries => {\n  const getByHintText = getBy(instance);\n  const getAllByHintText = getAllBy(instance);\n  const queryByHintText = queryBy(instance);\n  const queryAllByHintText = queryAllBy(instance);\n  const findByHintText = findBy(instance);\n  const findAllByHintText = findAllBy(instance);\n\n  return {\n    getByHintText,\n    getAllByHintText,\n    queryByHintText,\n    queryAllByHintText,\n    findByHintText,\n    findAllByHintText,\n\n    // a11yHint aliases\n    getByA11yHint: getByHintText,\n    getAllByA11yHint: getAllByHintText,\n    queryByA11yHint: queryByHintText,\n    queryAllByA11yHint: queryAllByHintText,\n    findByA11yHint: findByHintText,\n    findAllByA11yHint: findAllByHintText,\n\n    // accessibilityHint aliases\n    getByAccessibilityHint: getByHintText,\n    getAllByAccessibilityHint: getAllByHintText,\n    queryByAccessibilityHint: queryByHintText,\n    queryAllByAccessibilityHint: queryAllByHintText,\n    findByAccessibilityHint: findByHintText,\n    findAllByAccessibilityHint: findAllByHintText,\n  };\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AASA,IAAAE,YAAA,GAAAF,OAAA;AAKA,MAAMG,iBAAiB,GAAGA,CACxBC,IAAuB,EACvBC,IAAe,EACfC,OAAyB,GAAG,CAAC,CAAC,KAC3B;EACH,MAAM;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGF,OAAO;EACrC,OAAO,IAAAG,gBAAO,EAACJ,IAAI,EAAED,IAAI,CAACM,KAAK,CAACC,iBAAiB,EAAEH,UAAU,EAAED,KAAK,CAAC;AACvE,CAAC;AAED,MAAMK,kBAAkB,GACtBC,QAA2B,IAE3B,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,YAAY,EAAE;EAChD,OAAO,IAAAC,gBAAO,EAACJ,QAAQ,EAAGT,IAAI,IAAKD,iBAAiB,CAACC,IAAI,EAAEW,IAAI,EAAEC,YAAY,CAAC,EAAEA,YAAY,CAAC;AAC/F,CAAC;AAEH,MAAME,gBAAgB,GAAIH,IAAe,IACvC,oDAAoDI,MAAM,CAACJ,IAAI,CAAC,GAAG;AACrE,MAAMK,eAAe,GAAIL,IAAe,IACtC,sDAAsDI,MAAM,CAACJ,IAAI,CAAC,EAAE;AAEtE,MAAM;EAAEM,KAAK;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAU,CAAC,GAAG,IAAAC,wBAAW,EAC7Ef,kBAAkB,EAClBQ,eAAe,EACfF,gBACF,CAAC;AA2BM,MAAMU,qBAAqB,GAAIf,QAA2B,IAAwB;EACvF,MAAMgB,aAAa,GAAGR,KAAK,CAACR,QAAQ,CAAC;EACrC,MAAMiB,gBAAgB,GAAGR,QAAQ,CAACT,QAAQ,CAAC;EAC3C,MAAMkB,eAAe,GAAGR,OAAO,CAACV,QAAQ,CAAC;EACzC,MAAMD,kBAAkB,GAAGY,UAAU,CAACX,QAAQ,CAAC;EAC/C,MAAMmB,cAAc,GAAGP,MAAM,CAACZ,QAAQ,CAAC;EACvC,MAAMoB,iBAAiB,GAAGP,SAAS,CAACb,QAAQ,CAAC;EAE7C,OAAO;IACLgB,aAAa;IACbC,gBAAgB;IAChBC,eAAe;IACfnB,kBAAkB;IAClBoB,cAAc;IACdC,iBAAiB;IAEjB;IACAC,aAAa,EAAEL,aAAa;IAC5BM,gBAAgB,EAAEL,gBAAgB;IAClCM,eAAe,EAAEL,eAAe;IAChCM,kBAAkB,EAAEzB,kBAAkB;IACtC0B,cAAc,EAAEN,cAAc;IAC9BO,iBAAiB,EAAEN,iBAAiB;IAEpC;IACAO,sBAAsB,EAAEX,aAAa;IACrCY,yBAAyB,EAAEX,gBAAgB;IAC3CY,wBAAwB,EAAEX,eAAe;IACzCY,2BAA2B,EAAE/B,kBAAkB;IAC/CgC,uBAAuB,EAAEZ,cAAc;IACvCa,0BAA0B,EAAEZ;EAC9B,CAAC;AACH,CAAC;AAACa,OAAA,CAAAlB,qBAAA,GAAAA,qBAAA", "ignoreList": []}