import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  Alert,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { loadSettings } from '../store/slices/settingsSlice';
import { loadUsageRecords, calculateUsageSinceLastRecording } from '../store/slices/usageSlice';
import { loadPurchaseTotals } from '../store/slices/purchasesSlice';
import { setCurrentUnits, setShowLowUnitsWarning } from '../store/slices/appSlice';
import {
  Container,
  Card,
  ThemedText,
  GradientButton,
} from '../components/common/StyledComponents';
import ModernDial from '../components/dashboard/ModernDial';
import { useTheme } from '../theme/ThemeContext';
import { DrawerParamList } from '../navigation/AppNavigator';
import Icon from 'react-native-vector-icons/MaterialIcons';

type DashboardNavigationProp = DrawerNavigationProp<DrawerParamList, 'Dashboard'>;

const DashboardScreen = () => {
  const navigation = useNavigation<DashboardNavigationProp>();
  const dispatch = useAppDispatch();
  const { theme, font } = useTheme();

  const [refreshing, setRefreshing] = useState(false);
  const screenWidth = Dimensions.get('window').width;

  // Redux state
  const settings = useAppSelector((state) => state.settings.settings);
  const { currentUnits, showLowUnitsWarning } = useAppSelector((state) => state.app);
  const { usageSinceLastRecording, weeklyUsage, monthlyUsage, weeklyUsageCost, monthlyUsageCost } = useAppSelector((state) => state.usage);
  const { weeklyTotal: weeklyPurchases, monthlyTotal: monthlyPurchases } = useAppSelector((state) => state.purchases);

  useEffect(() => {
    loadDashboardData();
  }, []);

  useEffect(() => {
    // Check for low units warning
    if (settings && currentUnits <= settings.thresholdLimit) {
      dispatch(setShowLowUnitsWarning(true));
      if (currentUnits <= settings.thresholdLimit) {
        Alert.alert(
          'Low Units Warning',
          `Your current units (${currentUnits.toFixed(1)} ${settings.unitType}) are below the threshold limit (${settings.thresholdLimit} ${settings.unitType}). Consider purchasing more electricity.`,
          [{ text: 'OK' }]
        );
      }
    } else {
      dispatch(setShowLowUnitsWarning(false));
    }
  }, [currentUnits, settings, dispatch]);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        dispatch(loadSettings()),
        dispatch(loadUsageRecords()),
        dispatch(loadPurchaseTotals()),
        dispatch(calculateUsageSinceLastRecording(currentUnits)),
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleQuickAction = (screen: keyof DrawerParamList) => {
    navigation.navigate(screen);
  };

  if (!settings) {
    return (
      <Container>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ThemedText variant="h2">Loading...</ThemedText>
        </View>
      </Container>
    );
  }

  const dialSize = Math.min(screenWidth * 0.6, 240);
  const currencySymbol = settings.currencyType === 'Custom' ? settings.customCurrencyName || 'CUR' : settings.currencyType;
  const unitName = settings.unitType === 'Custom' ? settings.customUnitName || 'Units' : settings.unitType;

  return (
    <Container>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Modern Dial Display */}
        <Card style={{ alignItems: 'center', marginVertical: 20 }}>
          <ModernDial
            currentValue={currentUnits}
            maxValue={100} // This could be dynamic based on typical usage
            title="Current Units"
            subtitle={`Usage since last: ${usageSinceLastRecording.toFixed(1)} ${unitName}`}
            unit={unitName}
            size={dialSize}
            showWarning={showLowUnitsWarning}
          />
        </Card>

        {/* Quick Action Buttons */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            Quick Actions
          </ThemedText>

          <GradientButton
            title="Add Purchase"
            icon="shopping-cart"
            onPress={() => handleQuickAction('Purchases')}
            style={{ marginBottom: 12 }}
          />

          <GradientButton
            title="Record Usage"
            icon="trending-up"
            onPress={() => handleQuickAction('Usage')}
            style={{ marginBottom: 12 }}
          />

          <GradientButton
            title="View History"
            icon="history"
            onPress={() => handleQuickAction('History')}
            variant="secondary"
          />
        </Card>

        {/* Usage Summary */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            Usage Summary
          </ThemedText>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Icon name="date-range" size={24} color={theme.colors.primary} />
              <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                Weekly Usage
              </ThemedText>
              <ThemedText variant="h3" color="primary">
                {weeklyUsage.toFixed(1)} {unitName}
              </ThemedText>
              <ThemedText variant="caption" color="textSecondary">
                {currencySymbol} {weeklyUsageCost.toFixed(2)}
              </ThemedText>
            </View>

            <View style={{ width: 1, backgroundColor: theme.colors.border, marginHorizontal: 16 }} />

            <View style={{ flex: 1, alignItems: 'center' }}>
              <Icon name="calendar-today" size={24} color={theme.colors.primary} />
              <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                Monthly Usage
              </ThemedText>
              <ThemedText variant="h3" color="primary">
                {monthlyUsage.toFixed(1)} {unitName}
              </ThemedText>
              <ThemedText variant="caption" color="textSecondary">
                {currencySymbol} {monthlyUsageCost.toFixed(2)}
              </ThemedText>
            </View>
          </View>
        </Card>

        {/* Purchase Summary */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            Purchase Summary
          </ThemedText>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Icon name="shopping-cart" size={24} color={theme.colors.secondary} />
              <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                Weekly Purchases
              </ThemedText>
              <ThemedText variant="h3" color="secondary">
                {currencySymbol} {weeklyPurchases.toFixed(2)}
              </ThemedText>
            </View>

            <View style={{ width: 1, backgroundColor: theme.colors.border, marginHorizontal: 16 }} />

            <View style={{ flex: 1, alignItems: 'center' }}>
              <Icon name="account-balance-wallet" size={24} color={theme.colors.secondary} />
              <ThemedText variant="caption" color="textSecondary" style={{ marginTop: 4 }}>
                Monthly Purchases
              </ThemedText>
              <ThemedText variant="h3" color="secondary">
                {currencySymbol} {monthlyPurchases.toFixed(2)}
              </ThemedText>
            </View>
          </View>
        </Card>

        {/* Settings Info */}
        <Card>
          <ThemedText variant="h3" style={{ marginBottom: 16 }}>
            Current Settings
          </ThemedText>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
            <ThemedText color="textSecondary">Unit Cost:</ThemedText>
            <ThemedText>{currencySymbol} {settings.unitCost.toFixed(3)} per {unitName}</ThemedText>
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
            <ThemedText color="textSecondary">Threshold Limit:</ThemedText>
            <ThemedText>{settings.thresholdLimit} {unitName}</ThemedText>
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <ThemedText color="textSecondary">Theme:</ThemedText>
            <ThemedText>{theme.name}</ThemedText>
          </View>
        </Card>
      </ScrollView>
    </Container>
  );
};

export default DashboardScreen;
