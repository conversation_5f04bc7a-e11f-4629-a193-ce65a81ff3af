import SQLite from 'react-native-sqlite-storage';

// Enable debugging
SQLite.DEBUG(true);
SQLite.enablePromise(true);

export interface Purchase {
  id?: number;
  currency: number;
  units: number;
  unitCost: number;
  timestamp: string;
  currencyType: string;
}

export interface UsageRecord {
  id?: number;
  previousUnits: number;
  currentUnits: number;
  usageDifference: number;
  timestamp: string;
  cost: number;
}

export interface Settings {
  id?: number;
  unitCost: number;
  thresholdLimit: number;
  currencyType: string;
  unitType: string;
  customCurrencyName?: string;
  customUnitName?: string;
  theme: string;
  fontStyle: string;
  notificationsEnabled: boolean;
  notificationTime: string;
}

export interface HistoryEntry {
  id?: number;
  type: 'purchase' | 'usage';
  data: Purchase | UsageRecord;
  timestamp: string;
}

class DatabaseManager {
  private database: SQLite.SQLiteDatabase | null = null;

  async initDatabase(): Promise<void> {
    try {
      this.database = await SQLite.openDatabase({
        name: 'PrepaidElectricity.db',
        location: 'default',
      });

      await this.createTables();
      await this.insertDefaultSettings();
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.database) throw new Error('Database not initialized');

    const createPurchasesTable = `
      CREATE TABLE IF NOT EXISTS purchases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        currency REAL NOT NULL,
        units REAL NOT NULL,
        unitCost REAL NOT NULL,
        timestamp TEXT NOT NULL,
        currencyType TEXT NOT NULL
      );
    `;

    const createUsageRecordsTable = `
      CREATE TABLE IF NOT EXISTS usage_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        previousUnits REAL NOT NULL,
        currentUnits REAL NOT NULL,
        usageDifference REAL NOT NULL,
        timestamp TEXT NOT NULL,
        cost REAL NOT NULL
      );
    `;

    const createSettingsTable = `
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        unitCost REAL NOT NULL DEFAULT 0.15,
        thresholdLimit REAL NOT NULL DEFAULT 10.0,
        currencyType TEXT NOT NULL DEFAULT 'USD',
        unitType TEXT NOT NULL DEFAULT 'Units',
        customCurrencyName TEXT,
        customUnitName TEXT,
        theme TEXT NOT NULL DEFAULT 'default',
        fontStyle TEXT NOT NULL DEFAULT 'default',
        notificationsEnabled INTEGER NOT NULL DEFAULT 1,
        notificationTime TEXT NOT NULL DEFAULT '18:00'
      );
    `;

    const createHistoryTable = `
      CREATE TABLE IF NOT EXISTS history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp TEXT NOT NULL
      );
    `;

    await this.database.executeSql(createPurchasesTable);
    await this.database.executeSql(createUsageRecordsTable);
    await this.database.executeSql(createSettingsTable);
    await this.database.executeSql(createHistoryTable);
  }

  private async insertDefaultSettings(): Promise<void> {
    if (!this.database) throw new Error('Database not initialized');

    const checkSettings = 'SELECT COUNT(*) as count FROM settings';
    const result = await this.database.executeSql(checkSettings);
    const count = result[0].rows.item(0).count;

    if (count === 0) {
      const insertDefaultSettings = `
        INSERT INTO settings (
          unitCost, thresholdLimit, currencyType, unitType, 
          theme, fontStyle, notificationsEnabled, notificationTime
        ) VALUES (0.15, 10.0, 'USD', 'Units', 'default', 'default', 1, '18:00')
      `;
      await this.database.executeSql(insertDefaultSettings);
    }
  }

  // Purchase operations
  async addPurchase(purchase: Omit<Purchase, 'id'>): Promise<number> {
    if (!this.database) throw new Error('Database not initialized');

    const insertPurchase = `
      INSERT INTO purchases (currency, units, unitCost, timestamp, currencyType)
      VALUES (?, ?, ?, ?, ?)
    `;

    const result = await this.database.executeSql(insertPurchase, [
      purchase.currency,
      purchase.units,
      purchase.unitCost,
      purchase.timestamp,
      purchase.currencyType,
    ]);

    const purchaseId = result[0].insertId;

    // Add to history
    await this.addToHistory('purchase', purchase, purchase.timestamp);

    return purchaseId;
  }

  async getPurchases(): Promise<Purchase[]> {
    if (!this.database) throw new Error('Database not initialized');

    const selectPurchases = 'SELECT * FROM purchases ORDER BY timestamp DESC';
    const result = await this.database.executeSql(selectPurchases);

    const purchases: Purchase[] = [];
    for (let i = 0; i < result[0].rows.length; i++) {
      purchases.push(result[0].rows.item(i));
    }

    return purchases;
  }

  // Usage record operations
  async addUsageRecord(usageRecord: Omit<UsageRecord, 'id'>): Promise<number> {
    if (!this.database) throw new Error('Database not initialized');

    const insertUsageRecord = `
      INSERT INTO usage_records (previousUnits, currentUnits, usageDifference, timestamp, cost)
      VALUES (?, ?, ?, ?, ?)
    `;

    const result = await this.database.executeSql(insertUsageRecord, [
      usageRecord.previousUnits,
      usageRecord.currentUnits,
      usageRecord.usageDifference,
      usageRecord.timestamp,
      usageRecord.cost,
    ]);

    const recordId = result[0].insertId;

    // Add to history
    await this.addToHistory('usage', usageRecord, usageRecord.timestamp);

    return recordId;
  }

  async getUsageRecords(): Promise<UsageRecord[]> {
    if (!this.database) throw new Error('Database not initialized');

    const selectUsageRecords = 'SELECT * FROM usage_records ORDER BY timestamp DESC';
    const result = await this.database.executeSql(selectUsageRecords);

    const usageRecords: UsageRecord[] = [];
    for (let i = 0; i < result[0].rows.length; i++) {
      usageRecords.push(result[0].rows.item(i));
    }

    return usageRecords;
  }

  async getLatestUsageRecord(): Promise<UsageRecord | null> {
    if (!this.database) throw new Error('Database not initialized');

    const selectLatest = 'SELECT * FROM usage_records ORDER BY timestamp DESC LIMIT 1';
    const result = await this.database.executeSql(selectLatest);

    if (result[0].rows.length > 0) {
      return result[0].rows.item(0);
    }

    return null;
  }

  // Settings operations
  async getSettings(): Promise<Settings> {
    if (!this.database) throw new Error('Database not initialized');

    const selectSettings = 'SELECT * FROM settings LIMIT 1';
    const result = await this.database.executeSql(selectSettings);

    if (result[0].rows.length > 0) {
      const settings = result[0].rows.item(0);
      return {
        ...settings,
        notificationsEnabled: Boolean(settings.notificationsEnabled),
      };
    }

    throw new Error('Settings not found');
  }

  async updateSettings(settings: Partial<Settings>): Promise<void> {
    if (!this.database) throw new Error('Database not initialized');

    const updateFields = Object.keys(settings)
      .filter(key => key !== 'id')
      .map(key => `${key} = ?`)
      .join(', ');

    const values = Object.keys(settings)
      .filter(key => key !== 'id')
      .map(key => {
        const value = settings[key as keyof Settings];
        return typeof value === 'boolean' ? (value ? 1 : 0) : value;
      });

    const updateSettings = `UPDATE settings SET ${updateFields} WHERE id = 1`;
    await this.database.executeSql(updateSettings, values);
  }

  // History operations
  private async addToHistory(type: 'purchase' | 'usage', data: any, timestamp: string): Promise<void> {
    if (!this.database) throw new Error('Database not initialized');

    const insertHistory = `
      INSERT INTO history (type, data, timestamp)
      VALUES (?, ?, ?)
    `;

    await this.database.executeSql(insertHistory, [
      type,
      JSON.stringify(data),
      timestamp,
    ]);
  }

  async getHistory(): Promise<HistoryEntry[]> {
    if (!this.database) throw new Error('Database not initialized');

    const selectHistory = 'SELECT * FROM history ORDER BY timestamp DESC';
    const result = await this.database.executeSql(selectHistory);

    const history: HistoryEntry[] = [];
    for (let i = 0; i < result[0].rows.length; i++) {
      const item = result[0].rows.item(i);
      history.push({
        ...item,
        data: JSON.parse(item.data),
      });
    }

    return history;
  }

  // Reset operations
  async factoryReset(): Promise<void> {
    if (!this.database) throw new Error('Database not initialized');

    await this.database.executeSql('DELETE FROM purchases');
    await this.database.executeSql('DELETE FROM usage_records');
    await this.database.executeSql('DELETE FROM history');
    await this.database.executeSql('DELETE FROM settings');
    
    await this.insertDefaultSettings();
  }

  async dashboardDataReset(): Promise<void> {
    if (!this.database) throw new Error('Database not initialized');

    await this.database.executeSql('DELETE FROM usage_records');
    
    // Remove usage entries from history but keep purchases
    await this.database.executeSql('DELETE FROM history WHERE type = ?', ['usage']);
  }

  // Utility methods for calculations
  async getWeeklyTotals(): Promise<{ purchases: number; usage: number; cost: number }> {
    if (!this.database) throw new Error('Database not initialized');

    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    const weekAgoString = weekAgo.toISOString();

    const weeklyPurchases = await this.database.executeSql(
      'SELECT SUM(currency) as total FROM purchases WHERE timestamp >= ?',
      [weekAgoString]
    );

    const weeklyUsage = await this.database.executeSql(
      'SELECT SUM(usageDifference) as total, SUM(cost) as totalCost FROM usage_records WHERE timestamp >= ?',
      [weekAgoString]
    );

    return {
      purchases: weeklyPurchases[0].rows.item(0).total || 0,
      usage: weeklyUsage[0].rows.item(0).total || 0,
      cost: weeklyUsage[0].rows.item(0).totalCost || 0,
    };
  }

  async getMonthlyTotals(): Promise<{ purchases: number; usage: number; cost: number }> {
    if (!this.database) throw new Error('Database not initialized');

    const monthAgo = new Date();
    monthAgo.setMonth(monthAgo.getMonth() - 1);
    const monthAgoString = monthAgo.toISOString();

    const monthlyPurchases = await this.database.executeSql(
      'SELECT SUM(currency) as total FROM purchases WHERE timestamp >= ?',
      [monthAgoString]
    );

    const monthlyUsage = await this.database.executeSql(
      'SELECT SUM(usageDifference) as total, SUM(cost) as totalCost FROM usage_records WHERE timestamp >= ?',
      [monthAgoString]
    );

    return {
      purchases: monthlyPurchases[0].rows.item(0).total || 0,
      usage: monthlyUsage[0].rows.item(0).total || 0,
      cost: monthlyUsage[0].rows.item(0).totalCost || 0,
    };
  }

  async closeDatabase(): Promise<void> {
    if (this.database) {
      await this.database.close();
      this.database = null;
    }
  }
}

export default new DatabaseManager();
