{"version": 3, "file": "index.js", "names": ["_setup", "require", "userEvent", "exports", "setup", "press", "element", "longPress", "options", "type", "text", "clear", "paste", "scrollTo"], "sources": ["../../src/user-event/index.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport type { PressOptions } from './press';\nimport type { ScrollToOptions } from './scroll';\nimport { setup } from './setup';\nimport type { TypeOptions } from './type';\n\nexport { UserEventConfig } from './setup';\n\nexport const userEvent = {\n  setup,\n\n  // Direct access for User Event v13 compatibility\n  press: (element: ReactTestInstance) => setup().press(element),\n  longPress: (element: ReactTestInstance, options?: PressOptions) =>\n    setup().longPress(element, options),\n  type: (element: ReactTestInstance, text: string, options?: TypeOptions) =>\n    setup().type(element, text, options),\n  clear: (element: ReactTestInstance) => setup().clear(element),\n  paste: (element: ReactTestInstance, text: string) => setup().paste(element, text),\n  scrollTo: (element: ReactTestInstance, options: ScrollToOptions) =>\n    setup().scrollTo(element, options),\n};\n"], "mappings": ";;;;;;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKO,MAAMC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG;EACvBE,KAAK,EAALA,YAAK;EAEL;EACAC,KAAK,EAAGC,OAA0B,IAAK,IAAAF,YAAK,EAAC,CAAC,CAACC,KAAK,CAACC,OAAO,CAAC;EAC7DC,SAAS,EAAEA,CAACD,OAA0B,EAAEE,OAAsB,KAC5D,IAAAJ,YAAK,EAAC,CAAC,CAACG,SAAS,CAACD,OAAO,EAAEE,OAAO,CAAC;EACrCC,IAAI,EAAEA,CAACH,OAA0B,EAAEI,IAAY,EAAEF,OAAqB,KACpE,IAAAJ,YAAK,EAAC,CAAC,CAACK,IAAI,CAACH,OAAO,EAAEI,IAAI,EAAEF,OAAO,CAAC;EACtCG,KAAK,EAAGL,OAA0B,IAAK,IAAAF,YAAK,EAAC,CAAC,CAACO,KAAK,CAACL,OAAO,CAAC;EAC7DM,KAAK,EAAEA,CAACN,OAA0B,EAAEI,IAAY,KAAK,IAAAN,YAAK,EAAC,CAAC,CAACQ,KAAK,CAACN,OAAO,EAAEI,IAAI,CAAC;EACjFG,QAAQ,EAAEA,CAACP,OAA0B,EAAEE,OAAwB,KAC7D,IAAAJ,YAAK,EAAC,CAAC,CAACS,QAAQ,CAACP,OAAO,EAAEE,OAAO;AACrC,CAAC", "ignoreList": []}