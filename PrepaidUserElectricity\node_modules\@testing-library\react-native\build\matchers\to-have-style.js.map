{"version": 3, "file": "to-have-style.js", "names": ["_reactNative", "require", "_jestMatcherU<PERSON>s", "_utils", "toHaveStyle", "element", "style", "checkHostElement", "expected", "StyleSheet", "flatten", "received", "props", "pass", "Object", "keys", "every", "key", "equals", "message", "to", "isNot", "matcher", "matcherHint", "formatMessage", "formatStyles", "pickReceivedStyles", "expectedDiff", "join", "<PERSON><PERSON><PERSON><PERSON>", "diff", "result", "for<PERSON>ach", "undefined", "sort", "map", "prop", "Array", "isArray", "JSON", "stringify"], "sources": ["../../src/matchers/to-have-style.ts"], "sourcesContent": ["import type { ImageStyle, StyleProp, TextStyle, ViewStyle } from 'react-native';\nimport { StyleSheet } from 'react-native';\nimport type { ReactTestInstance } from 'react-test-renderer';\nimport { diff, matcherHint } from 'jest-matcher-utils';\n\nimport { checkHostElement, formatMessage } from './utils';\n\nexport type Style = ViewStyle | TextStyle | ImageStyle;\n\ntype StyleLike = Record<string, unknown>;\n\nexport function toHaveStyle(\n  this: jest.MatcherContext,\n  element: ReactTestInstance,\n  style: StyleProp<Style>,\n) {\n  checkHostElement(element, toHaveStyle, this);\n\n  const expected = (StyleSheet.flatten(style) as StyleLike) ?? {};\n  const received = (StyleSheet.flatten(element.props.style) as StyleLike) ?? {};\n\n  const pass = Object.keys(expected).every((key) => this.equals(expected[key], received[key]));\n\n  return {\n    pass,\n    message: () => {\n      const to = this.isNot ? 'not to' : 'to';\n      const matcher = matcherHint(`${this.isNot ? '.not' : ''}.toHaveStyle`, 'element', '');\n\n      if (pass) {\n        return formatMessage(\n          matcher,\n          `Expected element ${to} have style`,\n          formatStyles(expected),\n          'Received',\n          formatStyles(pickReceivedStyles(expected, received)),\n        );\n      } else {\n        return [matcher, '', expectedDiff(expected, received)].join('\\n');\n      }\n    },\n  };\n}\n\n/**\n * Generate diff between `expected` and `received` styles.\n */\nfunction expectedDiff(expected: StyleLike, received: StyleLike) {\n  const receivedNarrow = pickReceivedStyles(expected, received);\n  return diff(formatStyles(expected), formatStyles(receivedNarrow));\n}\n\n/**\n * Pick from `received` style only the keys present in `expected` style.\n */\nfunction pickReceivedStyles(expected: StyleLike, received: StyleLike) {\n  const result: StyleLike = {};\n  Object.keys(received).forEach((key) => {\n    if (expected[key] !== undefined) {\n      result[key] = received[key];\n    }\n  });\n\n  return result;\n}\n\nfunction formatStyles(style: StyleLike) {\n  return Object.keys(style)\n    .sort()\n    .map((prop) =>\n      Array.isArray(style[prop])\n        ? `${prop}: ${JSON.stringify(style[prop], null, 2)};`\n        : `${prop}: ${style[prop]};`,\n    )\n    .join('\\n');\n}\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,iBAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAMO,SAASG,WAAWA,CAEzBC,OAA0B,EAC1BC,KAAuB,EACvB;EACA,IAAAC,uBAAgB,EAACF,OAAO,EAAED,WAAW,EAAE,IAAI,CAAC;EAE5C,MAAMI,QAAQ,GAAIC,uBAAU,CAACC,OAAO,CAACJ,KAAK,CAAC,IAAkB,CAAC,CAAC;EAC/D,MAAMK,QAAQ,GAAIF,uBAAU,CAACC,OAAO,CAACL,OAAO,CAACO,KAAK,CAACN,KAAK,CAAC,IAAkB,CAAC,CAAC;EAE7E,MAAMO,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAAC,CAACQ,KAAK,CAAEC,GAAG,IAAK,IAAI,CAACC,MAAM,CAACV,QAAQ,CAACS,GAAG,CAAC,EAAEN,QAAQ,CAACM,GAAG,CAAC,CAAC,CAAC;EAE5F,OAAO;IACLJ,IAAI;IACJM,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,QAAQ,GAAG,IAAI;MACvC,MAAMC,OAAO,GAAG,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACF,KAAK,GAAG,MAAM,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC;MAErF,IAAIR,IAAI,EAAE;QACR,OAAO,IAAAW,oBAAa,EAClBF,OAAO,EACP,oBAAoBF,EAAE,aAAa,EACnCK,YAAY,CAACjB,QAAQ,CAAC,EACtB,UAAU,EACViB,YAAY,CAACC,kBAAkB,CAAClB,QAAQ,EAAEG,QAAQ,CAAC,CACrD,CAAC;MACH,CAAC,MAAM;QACL,OAAO,CAACW,OAAO,EAAE,EAAE,EAAEK,YAAY,CAACnB,QAAQ,EAAEG,QAAQ,CAAC,CAAC,CAACiB,IAAI,CAAC,IAAI,CAAC;MACnE;IACF;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASD,YAAYA,CAACnB,QAAmB,EAAEG,QAAmB,EAAE;EAC9D,MAAMkB,cAAc,GAAGH,kBAAkB,CAAClB,QAAQ,EAAEG,QAAQ,CAAC;EAC7D,OAAO,IAAAmB,sBAAI,EAACL,YAAY,CAACjB,QAAQ,CAAC,EAAEiB,YAAY,CAACI,cAAc,CAAC,CAAC;AACnE;;AAEA;AACA;AACA;AACA,SAASH,kBAAkBA,CAAClB,QAAmB,EAAEG,QAAmB,EAAE;EACpE,MAAMoB,MAAiB,GAAG,CAAC,CAAC;EAC5BjB,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACqB,OAAO,CAAEf,GAAG,IAAK;IACrC,IAAIT,QAAQ,CAACS,GAAG,CAAC,KAAKgB,SAAS,EAAE;MAC/BF,MAAM,CAACd,GAAG,CAAC,GAAGN,QAAQ,CAACM,GAAG,CAAC;IAC7B;EACF,CAAC,CAAC;EAEF,OAAOc,MAAM;AACf;AAEA,SAASN,YAAYA,CAACnB,KAAgB,EAAE;EACtC,OAAOQ,MAAM,CAACC,IAAI,CAACT,KAAK,CAAC,CACtB4B,IAAI,CAAC,CAAC,CACNC,GAAG,CAAEC,IAAI,IACRC,KAAK,CAACC,OAAO,CAAChC,KAAK,CAAC8B,IAAI,CAAC,CAAC,GACtB,GAAGA,IAAI,KAAKG,IAAI,CAACC,SAAS,CAAClC,KAAK,CAAC8B,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GACnD,GAAGA,IAAI,KAAK9B,KAAK,CAAC8B,IAAI,CAAC,GAC7B,CAAC,CACAR,IAAI,CAAC,IAAI,CAAC;AACf", "ignoreList": []}