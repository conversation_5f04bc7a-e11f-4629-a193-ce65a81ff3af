import PushNotification from 'react-native-push-notification';
import { Platform } from 'react-native';

class NotificationService {
  constructor() {
    this.configure();
  }

  configure() {
    PushNotification.configure({
      // (optional) Called when Token is generated (iOS and Android)
      onRegister: function (token) {
        console.log('TOKEN:', token);
      },

      // (required) Called when a remote is received or opened, or local notification is opened
      onNotification: function (notification) {
        console.log('NOTIFICATION:', notification);
      },

      // (optional) Called when Registered Action is pressed and invokeApp is false, if true onNotification will be called (Android)
      onAction: function (notification) {
        console.log('ACTION:', notification.action);
        console.log('NOTIFICATION:', notification);
      },

      // (optional) Called when the user fails to register for remote notifications. Typically occurs when APNS is having issues, or the device is a simulator. (iOS)
      onRegistrationError: function(err) {
        console.error(err.message, err);
      },

      // IOS ONLY (optional): default: all - Permissions to register.
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },

      // Should the initial notification be popped automatically
      // default: true
      popInitialNotification: true,

      /**
       * (optional) default: true
       * - Specified if permissions (ios) and token (android and ios) will requested or not,
       * - if not, you must call PushNotification.requestPermissions() later
       * - if you are not using remote notification or do not have Firebase installed, use this:
       *     requestPermissions: Platform.OS === 'ios'
       */
      requestPermissions: Platform.OS === 'ios',
    });

    // Create default channel for Android
    if (Platform.OS === 'android') {
      PushNotification.createChannel(
        {
          channelId: 'prepaid-electricity-channel',
          channelName: 'Prepaid Electricity Notifications',
          channelDescription: 'Daily reminders for electricity usage recording',
          playSound: true,
          soundName: 'default',
          importance: 4,
          vibrate: true,
        },
        (created) => console.log(`createChannel returned '${created}'`)
      );
    }
  }

  // Schedule daily notification
  scheduleDailyNotification(time: string, enabled: boolean) {
    // Cancel existing notifications first
    this.cancelAllNotifications();

    if (!enabled) {
      return;
    }

    const [hours, minutes] = time.split(':').map(Number);
    
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      console.error('Invalid time format for notification');
      return;
    }

    const now = new Date();
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);

    // If the time has already passed today, schedule for tomorrow
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    PushNotification.localNotificationSchedule({
      id: 'daily-usage-reminder',
      channelId: 'prepaid-electricity-channel',
      title: 'Electricity Usage Reminder',
      message: 'Don\'t forget to record your daily electricity usage!',
      date: scheduledTime,
      repeatType: 'day',
      actions: ['Record Usage', 'Dismiss'],
      category: 'USAGE_REMINDER',
      userInfo: {
        type: 'daily_reminder',
        action: 'record_usage',
      },
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
      smallIcon: 'ic_notification',
      largeIcon: 'ic_launcher',
      bigText: 'Tap to open the app and record your current electricity meter reading. Stay on top of your usage!',
      subText: 'Prepaid Electricity',
      color: '#2196F3',
      ongoing: false,
      priority: 'high',
      visibility: 'public',
      importance: 'high',
    });

    console.log(`Daily notification scheduled for ${time}`);
  }

  // Send immediate notification
  sendImmediateNotification(title: string, message: string, data?: any) {
    PushNotification.localNotification({
      channelId: 'prepaid-electricity-channel',
      title: title,
      message: message,
      userInfo: data || {},
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
      smallIcon: 'ic_notification',
      largeIcon: 'ic_launcher',
      color: '#2196F3',
      ongoing: false,
      priority: 'high',
      visibility: 'public',
      importance: 'high',
    });
  }

  // Send low units warning notification
  sendLowUnitsWarning(currentUnits: number, threshold: number, unitType: string) {
    this.sendImmediateNotification(
      'Low Units Warning!',
      `Your current units (${currentUnits.toFixed(1)} ${unitType}) are below the threshold (${threshold} ${unitType}). Consider purchasing more electricity.`,
      {
        type: 'low_units_warning',
        currentUnits,
        threshold,
        unitType,
      }
    );
  }

  // Send purchase confirmation notification
  sendPurchaseConfirmation(amount: number, units: number, currencyType: string, unitType: string) {
    this.sendImmediateNotification(
      'Purchase Recorded',
      `Successfully added ${units.toFixed(2)} ${unitType} for ${currencyType} ${amount.toFixed(2)}`,
      {
        type: 'purchase_confirmation',
        amount,
        units,
        currencyType,
        unitType,
      }
    );
  }

  // Send usage recording confirmation
  sendUsageConfirmation(usage: number, cost: number, currencyType: string, unitType: string) {
    this.sendImmediateNotification(
      'Usage Recorded',
      `Recorded ${usage.toFixed(2)} ${unitType} usage costing ${currencyType} ${cost.toFixed(2)}`,
      {
        type: 'usage_confirmation',
        usage,
        cost,
        currencyType,
        unitType,
      }
    );
  }

  // Cancel all notifications
  cancelAllNotifications() {
    PushNotification.cancelAllLocalNotifications();
  }

  // Cancel specific notification
  cancelNotification(id: string) {
    PushNotification.cancelLocalNotifications({ id });
  }

  // Check notification permissions
  checkPermissions(callback: (permissions: any) => void) {
    PushNotification.checkPermissions(callback);
  }

  // Request permissions (iOS)
  requestPermissions() {
    return PushNotification.requestPermissions();
  }

  // Get scheduled notifications
  getScheduledNotifications(callback: (notifications: any[]) => void) {
    PushNotification.getScheduledLocalNotifications(callback);
  }

  // Handle notification actions
  handleNotificationAction(notification: any) {
    const { userInfo, action } = notification;
    
    switch (userInfo?.type) {
      case 'daily_reminder':
        if (action === 'Record Usage') {
          // Navigate to usage screen
          // This would be handled by the navigation service
          console.log('Navigate to usage screen');
        }
        break;
      
      case 'low_units_warning':
        // Navigate to purchases screen
        console.log('Navigate to purchases screen');
        break;
      
      default:
        console.log('Unknown notification type:', userInfo?.type);
    }
  }

  // Update notification settings
  updateNotificationSettings(enabled: boolean, time: string) {
    this.scheduleDailyNotification(time, enabled);
  }

  // Test notification (for debugging)
  sendTestNotification() {
    this.sendImmediateNotification(
      'Test Notification',
      'This is a test notification from Prepaid Electricity app.',
      { type: 'test' }
    );
  }
}

export default new NotificationService();
