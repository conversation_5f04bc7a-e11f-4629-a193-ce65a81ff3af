<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prepaid User Electricity</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .dial-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .dial {
            width: 150px;
            height: 150px;
            border: 8px solid rgba(255, 255, 255, 0.3);
            border-top: 8px solid #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            position: relative;
            animation: spin 3s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .units {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .units-label {
            font-size: 18px;
            opacity: 0.8;
        }
        
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 30px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .feature-desc {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            opacity: 0.6;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">⚡</div>
            <div class="title">Prepaid User Electricity</div>
            <div class="subtitle">Smart Electricity Management</div>
        </div>
        
        <div class="dial-container">
            <div class="dial"></div>
            <div class="units">75.0</div>
            <div class="units-label">Units Remaining</div>
        </div>
        
        <div class="status">
            <strong>✅ System Ready</strong><br>
            Your electricity tracking app is running successfully!
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🛒</div>
                <div class="feature-title">Purchase Tracking</div>
                <div class="feature-desc">Record electricity purchases</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Usage Analytics</div>
                <div class="feature-desc">Monitor consumption patterns</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📈</div>
                <div class="feature-title">Smart Charts</div>
                <div class="feature-desc">Visual data representation</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔔</div>
                <div class="feature-title">Notifications</div>
                <div class="feature-desc">Daily usage reminders</div>
            </div>
        </div>
        
        <div class="footer">
            <p>Prepaid User Electricity v1.0.0</p>
            <p>Built with ❤️ for efficient electricity management</p>
        </div>
    </div>
</body>
</html>
