<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prepaid User Electricity</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 10px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .dial-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .dial {
            width: 150px;
            height: 150px;
            border: 8px solid rgba(255, 255, 255, 0.3);
            border-top: 8px solid #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            position: relative;
            animation: spin 3s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .units {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4CAF50;
        }
        
        .units-label {
            font-size: 18px;
            opacity: 0.8;
        }
        
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 30px 0;
        }
        
        .control-group {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .control-group h3 {
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
        }
        
        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .history {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .history h3 {
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-type {
            font-size: 20px;
            margin-right: 10px;
        }
        
        .history-details {
            flex: 1;
        }
        
        .history-amount {
            font-weight: bold;
            color: #4CAF50;
        }
        
        .summary {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .summary-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .summary-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .summary-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            opacity: 0.6;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">⚡</div>
            <div class="title">Prepaid User Electricity</div>
            <div class="subtitle">Smart Electricity Management</div>
        </div>
        
        <div class="dial-container">
            <div class="dial"></div>
            <div class="units" id="currentUnits">75.0</div>
            <div class="units-label">Units Remaining</div>
        </div>
        
        <div class="summary">
            <div class="summary-item">
                <div class="summary-value" id="weeklyUsage">25.0</div>
                <div class="summary-label">Weekly Usage</div>
            </div>
            <div class="summary-item">
                <div class="summary-value" id="monthlyCost">$15.00</div>
                <div class="summary-label">Monthly Cost</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h3>💰 Add Purchase</h3>
                <div class="input-group">
                    <label>Amount ($)</label>
                    <input type="number" id="purchaseAmount" placeholder="50.00" step="0.01">
                </div>
                <div class="input-group">
                    <label>Units Received</label>
                    <input type="number" id="purchaseUnits" placeholder="333.33" step="0.01">
                </div>
                <button class="btn" onclick="addPurchase()">Add Purchase</button>
            </div>
            
            <div class="control-group">
                <h3>📊 Record Usage</h3>
                <div class="input-group">
                    <label>Current Reading</label>
                    <input type="number" id="currentReading" placeholder="75.0" step="0.1">
                </div>
                <div class="input-group">
                    <label>Usage Amount</label>
                    <input type="number" id="usageAmount" placeholder="25.0" step="0.1">
                </div>
                <button class="btn" onclick="recordUsage()">Record Usage</button>
            </div>
        </div>
        
        <div class="history">
            <h3>📋 Recent Activity</h3>
            <div id="historyList">
                <div class="history-item">
                    <span class="history-type">🛒</span>
                    <div class="history-details">
                        <div>Purchase - $50.00</div>
                        <div style="font-size: 12px; opacity: 0.7;">Today, 2:30 PM</div>
                    </div>
                    <div class="history-amount">+333.3 Units</div>
                </div>
                <div class="history-item">
                    <span class="history-type">📈</span>
                    <div class="history-details">
                        <div>Usage Recorded</div>
                        <div style="font-size: 12px; opacity: 0.7;">Yesterday, 8:00 PM</div>
                    </div>
                    <div class="history-amount">-25.0 Units</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>Prepaid User Electricity v1.0.0</p>
            <p>Built with ❤️ for efficient electricity management</p>
        </div>
    </div>

    <script>
        let currentUnits = 75.0;
        let history = [
            { type: 'purchase', amount: 50.00, units: 333.3, time: 'Today, 2:30 PM' },
            { type: 'usage', units: 25.0, time: 'Yesterday, 8:00 PM' }
        ];

        function updateDisplay() {
            document.getElementById('currentUnits').textContent = currentUnits.toFixed(1);
            
            // Calculate weekly usage and monthly cost
            let weeklyUsage = history.filter(h => h.type === 'usage').reduce((sum, h) => sum + h.units, 0);
            let monthlyCost = history.filter(h => h.type === 'purchase').reduce((sum, h) => sum + h.amount, 0);
            
            document.getElementById('weeklyUsage').textContent = weeklyUsage.toFixed(1);
            document.getElementById('monthlyCost').textContent = '$' + monthlyCost.toFixed(2);
            
            updateHistory();
        }

        function updateHistory() {
            const historyList = document.getElementById('historyList');
            historyList.innerHTML = '';
            
            history.slice(-5).reverse().forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                
                if (item.type === 'purchase') {
                    historyItem.innerHTML = `
                        <span class="history-type">🛒</span>
                        <div class="history-details">
                            <div>Purchase - $${item.amount.toFixed(2)}</div>
                            <div style="font-size: 12px; opacity: 0.7;">${item.time}</div>
                        </div>
                        <div class="history-amount">+${item.units.toFixed(1)} Units</div>
                    `;
                } else {
                    historyItem.innerHTML = `
                        <span class="history-type">📈</span>
                        <div class="history-details">
                            <div>Usage Recorded</div>
                            <div style="font-size: 12px; opacity: 0.7;">${item.time}</div>
                        </div>
                        <div class="history-amount">-${item.units.toFixed(1)} Units</div>
                    `;
                }
                
                historyList.appendChild(historyItem);
            });
        }

        function addPurchase() {
            const amount = parseFloat(document.getElementById('purchaseAmount').value);
            const units = parseFloat(document.getElementById('purchaseUnits').value);
            
            if (amount && units) {
                currentUnits += units;
                history.push({
                    type: 'purchase',
                    amount: amount,
                    units: units,
                    time: new Date().toLocaleString()
                });
                
                document.getElementById('purchaseAmount').value = '';
                document.getElementById('purchaseUnits').value = '';
                
                updateDisplay();
                
                // Show success feedback
                alert(`✅ Purchase added successfully!\n+${units.toFixed(1)} units for $${amount.toFixed(2)}`);
            } else {
                alert('Please enter both amount and units');
            }
        }

        function recordUsage() {
            const reading = parseFloat(document.getElementById('currentReading').value);
            const usage = parseFloat(document.getElementById('usageAmount').value);
            
            if (usage) {
                currentUnits -= usage;
                history.push({
                    type: 'usage',
                    units: usage,
                    time: new Date().toLocaleString()
                });
                
                document.getElementById('currentReading').value = '';
                document.getElementById('usageAmount').value = '';
                
                updateDisplay();
                
                // Show success feedback
                alert(`✅ Usage recorded successfully!\n-${usage.toFixed(1)} units used`);
                
                // Low units warning
                if (currentUnits < 20) {
                    alert('⚠️ Warning: Low units remaining! Consider purchasing more electricity.');
                }
            } else {
                alert('Please enter usage amount');
            }
        }

        // Auto-calculate units when purchase amount is entered
        document.getElementById('purchaseAmount').addEventListener('input', function() {
            const amount = parseFloat(this.value);
            if (amount) {
                const unitCost = 0.15; // $0.15 per unit
                const units = amount / unitCost;
                document.getElementById('purchaseUnits').value = units.toFixed(2);
            }
        });

        // Auto-calculate usage when current reading is entered
        document.getElementById('currentReading').addEventListener('input', function() {
            const reading = parseFloat(this.value);
            if (reading) {
                const usage = Math.max(0, currentUnits - reading);
                document.getElementById('usageAmount').value = usage.toFixed(1);
            }
        });

        // Initialize display
        updateDisplay();
    </script>
</body>
</html>
