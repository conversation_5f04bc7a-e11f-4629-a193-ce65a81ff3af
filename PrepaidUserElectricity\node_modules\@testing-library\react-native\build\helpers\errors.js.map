{"version": 3, "file": "errors.js", "names": ["_prettyFormat", "_interopRequireDefault", "require", "e", "__esModule", "default", "ErrorWithStack", "Error", "constructor", "message", "callsite", "captureStackTrace", "exports", "prepareErrorMessage", "error", "name", "value", "errorMessage", "replace", "toString", "prettyFormat", "min", "createQueryByError", "includes", "copyStackTrace", "target", "stackTraceSource", "stack"], "sources": ["../../src/helpers/errors.ts"], "sourcesContent": ["import prettyFormat from 'pretty-format';\n\nexport class ErrorWithStack extends Error {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\n  constructor(message: string | undefined, callsite: Function) {\n    super(message);\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, callsite);\n    }\n  }\n}\n\nexport const prepareErrorMessage = (\n  // TS states that error caught in a catch close are of type `unknown`\n  // most real cases will be `<PERSON><PERSON>r`, but better safe than sorry\n  error: unknown,\n  name?: string,\n  value?: unknown,\n): string => {\n  let errorMessage: string;\n  if (error instanceof Error) {\n    // Strip info about custom predicate\n    errorMessage = error.message.replace(/ matching custom predicate[^]*/gm, '');\n  } else if (error && typeof error === 'object') {\n    errorMessage = error.toString();\n  } else {\n    errorMessage = 'Caught unknown error';\n  }\n\n  if (name && value) {\n    errorMessage += ` with ${name} ${prettyFormat(value, { min: true })}`;\n  }\n  return errorMessage;\n};\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport const createQueryByError = (error: unknown, callsite: Function): null => {\n  if (error instanceof Error) {\n    if (error.message.includes('No instances found')) {\n      return null;\n    }\n    throw new ErrorWithStack(error.message, callsite);\n  }\n\n  throw new ErrorWithStack(\n    `Query: caught unknown error type: ${typeof error}, value: ${error}`,\n    callsite,\n  );\n};\n\nexport function copyStackTrace(target: unknown, stackTraceSource: Error) {\n  if (target instanceof Error && stackTraceSource.stack) {\n    target.stack = stackTraceSource.stack.replace(stackTraceSource.message, target.message);\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAyC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAElC,MAAMG,cAAc,SAASC,KAAK,CAAC;EACxC;EACAC,WAAWA,CAACC,OAA2B,EAAEC,QAAkB,EAAE;IAC3D,KAAK,CAACD,OAAO,CAAC;IACd,IAAIF,KAAK,CAACI,iBAAiB,EAAE;MAC3BJ,KAAK,CAACI,iBAAiB,CAAC,IAAI,EAAED,QAAQ,CAAC;IACzC;EACF;AACF;AAACE,OAAA,CAAAN,cAAA,GAAAA,cAAA;AAEM,MAAMO,mBAAmB,GAAGA,CAGjCC,KAAc,EACdC,IAAa,EACbC,KAAe,KACJ;EACX,IAAIC,YAAoB;EACxB,IAAIH,KAAK,YAAYP,KAAK,EAAE;IAC1B;IACAU,YAAY,GAAGH,KAAK,CAACL,OAAO,CAACS,OAAO,CAAC,kCAAkC,EAAE,EAAE,CAAC;EAC9E,CAAC,MAAM,IAAIJ,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7CG,YAAY,GAAGH,KAAK,CAACK,QAAQ,CAAC,CAAC;EACjC,CAAC,MAAM;IACLF,YAAY,GAAG,sBAAsB;EACvC;EAEA,IAAIF,IAAI,IAAIC,KAAK,EAAE;IACjBC,YAAY,IAAI,SAASF,IAAI,IAAI,IAAAK,qBAAY,EAACJ,KAAK,EAAE;MAAEK,GAAG,EAAE;IAAK,CAAC,CAAC,EAAE;EACvE;EACA,OAAOJ,YAAY;AACrB,CAAC;;AAED;AAAAL,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AACO,MAAMS,kBAAkB,GAAGA,CAACR,KAAc,EAAEJ,QAAkB,KAAW;EAC9E,IAAII,KAAK,YAAYP,KAAK,EAAE;IAC1B,IAAIO,KAAK,CAACL,OAAO,CAACc,QAAQ,CAAC,oBAAoB,CAAC,EAAE;MAChD,OAAO,IAAI;IACb;IACA,MAAM,IAAIjB,cAAc,CAACQ,KAAK,CAACL,OAAO,EAAEC,QAAQ,CAAC;EACnD;EAEA,MAAM,IAAIJ,cAAc,CACtB,qCAAqC,OAAOQ,KAAK,YAAYA,KAAK,EAAE,EACpEJ,QACF,CAAC;AACH,CAAC;AAACE,OAAA,CAAAU,kBAAA,GAAAA,kBAAA;AAEK,SAASE,cAAcA,CAACC,MAAe,EAAEC,gBAAuB,EAAE;EACvE,IAAID,MAAM,YAAYlB,KAAK,IAAImB,gBAAgB,CAACC,KAAK,EAAE;IACrDF,MAAM,CAACE,KAAK,GAAGD,gBAAgB,CAACC,KAAK,CAACT,OAAO,CAACQ,gBAAgB,CAACjB,OAAO,EAAEgB,MAAM,CAAChB,OAAO,CAAC;EACzF;AACF", "ignoreList": []}