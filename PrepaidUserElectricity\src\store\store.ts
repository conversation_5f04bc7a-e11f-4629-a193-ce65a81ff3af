import { configureStore } from '@reduxjs/toolkit';
import appSlice from './slices/appSlice';
import settingsSlice from './slices/settingsSlice';
import purchasesSlice from './slices/purchasesSlice';
import usageSlice from './slices/usageSlice';
import historySlice from './slices/historySlice';

export const store = configureStore({
  reducer: {
    app: appSlice,
    settings: settingsSlice,
    purchases: purchasesSlice,
    usage: usageSlice,
    history: historySlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
