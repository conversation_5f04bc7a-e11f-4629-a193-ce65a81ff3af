{"name": "PrepaidUserElectricity", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native/new-app-screen": "0.80.0", "@react-navigation/drawer": "^7.4.2", "@react-navigation/native": "^7.1.11", "@react-navigation/stack": "^7.3.4", "@reduxjs/toolkit": "^2.8.2", "@tamagui/animations-react-native": "^1.126.18", "@tamagui/config": "^1.126.18", "@tamagui/core": "^1.126.18", "react": "19.1.0", "react-native": "0.80.0", "react-native-gesture-handler": "^2.26.0", "react-native-linear-gradient": "^2.8.3", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "victory-native": "^41.17.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-sqlite-storage": "^6.0.5", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}