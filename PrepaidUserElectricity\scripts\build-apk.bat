@echo off
echo Building Prepaid User Electricity APK...
echo.

REM Check if we're in the correct directory
if not exist "package.json" (
    echo Error: package.json not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

REM Check if Android SDK is available
if "%ANDROID_HOME%"=="" (
    echo Warning: ANDROID_HOME environment variable is not set.
    echo Please ensure Android SDK is installed and ANDROID_HOME is configured.
    echo.
)

REM Clean previous builds
echo Cleaning previous builds...
call npx react-native clean
if errorlevel 1 (
    echo Warning: Clean command failed, continuing anyway...
)

REM Install dependencies
echo Installing dependencies...
call npm install
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

REM Generate bundle
echo Generating React Native bundle...
call npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/
if errorlevel 1 (
    echo Error: Failed to generate bundle
    pause
    exit /b 1
)

REM Build debug APK
echo Building debug APK...
cd android
call gradlew assembleDebug
if errorlevel 1 (
    echo Error: Failed to build debug APK
    cd ..
    pause
    exit /b 1
)

REM Build release APK
echo Building release APK...
call gradlew assembleRelease
if errorlevel 1 (
    echo Error: Failed to build release APK
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo Build completed successfully!
echo.
echo APK files generated:
echo - Debug APK: android\app\build\outputs\apk\debug\app-debug.apk
echo - Release APK: android\app\build\outputs\apk\release\app-release.apk
echo.
echo You can install the APK on your Android device or upload to Google Play Store.
echo.
pause
