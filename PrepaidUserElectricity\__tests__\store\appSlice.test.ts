import appSlice, {
  setInitialized,
  setLoading,
  setCurrentUnits,
  setHasInitialValues,
  setShowLowUnitsWarning,
  setLastRecordingTimestamp,
  setError,
  clearError,
  resetApp,
  AppState,
} from '../../src/store/slices/appSlice';

describe('appSlice', () => {
  const initialState: AppState = {
    isInitialized: false,
    isLoading: false,
    currentUnits: 0,
    hasInitialValues: false,
    showLowUnitsWarning: false,
    lastRecordingTimestamp: null,
    error: null,
  };

  it('should return the initial state', () => {
    expect(appSlice(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('should handle setInitialized', () => {
    const actual = appSlice(initialState, setInitialized(true));
    expect(actual.isInitialized).toBe(true);
  });

  it('should handle setLoading', () => {
    const actual = appSlice(initialState, setLoading(true));
    expect(actual.isLoading).toBe(true);
  });

  it('should handle setCurrentUnits', () => {
    const actual = appSlice(initialState, setCurrentUnits(75.5));
    expect(actual.currentUnits).toBe(75.5);
  });

  it('should handle setHasInitialValues', () => {
    const actual = appSlice(initialState, setHasInitialValues(true));
    expect(actual.hasInitialValues).toBe(true);
  });

  it('should handle setShowLowUnitsWarning', () => {
    const actual = appSlice(initialState, setShowLowUnitsWarning(true));
    expect(actual.showLowUnitsWarning).toBe(true);
  });

  it('should handle setLastRecordingTimestamp', () => {
    const timestamp = new Date().toISOString();
    const actual = appSlice(initialState, setLastRecordingTimestamp(timestamp));
    expect(actual.lastRecordingTimestamp).toBe(timestamp);
  });

  it('should handle setError', () => {
    const errorMessage = 'Test error';
    const actual = appSlice(initialState, setError(errorMessage));
    expect(actual.error).toBe(errorMessage);
  });

  it('should handle clearError', () => {
    const stateWithError = { ...initialState, error: 'Test error' };
    const actual = appSlice(stateWithError, clearError());
    expect(actual.error).toBe(null);
  });

  it('should handle resetApp', () => {
    const modifiedState: AppState = {
      isInitialized: true,
      isLoading: true,
      currentUnits: 50,
      hasInitialValues: true,
      showLowUnitsWarning: true,
      lastRecordingTimestamp: new Date().toISOString(),
      error: 'Some error',
    };

    const actual = appSlice(modifiedState, resetApp());
    expect(actual).toEqual({
      ...initialState,
      isInitialized: true, // Should remain true after reset
    });
  });
});
