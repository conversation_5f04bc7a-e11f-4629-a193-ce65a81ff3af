# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Reanimated
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\node_modules\react-native-reanimated\android\.cxx\RelWithDebInfo\4r1u6l6j\arm64-v8a" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\node_modules\react-native-reanimated\android\.cxx\RelWithDebInfo\4r1u6l6j\arm64-v8a" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\node_modules\react-native-reanimated\android" -B"C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\node_modules\react-native-reanimated\android\.cxx\RelWithDebInfo\4r1u6l6j\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target worklets


#############################################
# Order-only phony target for worklets

build cmake_object_order_depends_target_worklets: phony || src/main/cpp/worklets/CMakeFiles/worklets.dir

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules\WorkletsModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules\WorkletsModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Registries\EventHandlerRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Registries
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Registries\WorkletRuntimeRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Registries
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\SharedItems\Shareables.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\SharedItems
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\AsyncQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\JSISerializer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\JSLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\JSScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\ReanimatedJSIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\ReanimatedVersion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\UIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\WorkletEventHandler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\RNRuntimeWorkletDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\ReanimatedHermesRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\ReanimatedRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\WorkletRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Augment-Projects/In_Production_Builds/Prepaid_User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\WorkletRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\colgr\Documents\Augment-Projects\In_Production_Builds\Prepaid_User\PrepaidUserElectricity\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\AndroidUIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\PlatformLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\WorkletsModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o: CXX_COMPILER__worklets_RelWithDebInfo C$:/Users/<USER>/Documents/Augment-Projects/In$ Production$ Builds/Prepaid$ User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\WorkletsOnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\RelWithDebInfo\4r1u6l6j\obj\arm64-v8a\libworklets.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target 