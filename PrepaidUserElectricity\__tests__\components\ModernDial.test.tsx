import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ModernDial from '../../src/components/dashboard/ModernDial';
import { ThemeProvider } from '../../src/theme/ThemeContext';
import settingsSlice from '../../src/store/slices/settingsSlice';

// Mock react-native-svg
jest.mock('react-native-svg', () => {
  const React = require('react');
  const { View } = require('react-native');
  
  return {
    Svg: ({ children, ...props }: any) => React.createElement(View, props, children),
    Circle: (props: any) => React.createElement(View, props),
    Path: (props: any) => React.createElement(View, props),
    Defs: ({ children }: any) => React.createElement(View, {}, children),
    LinearGradient: ({ children }: any) => React.createElement(View, {}, children),
    Stop: (props: any) => React.createElement(View, props),
  };
});

const createMockStore = () => {
  return configureStore({
    reducer: {
      settings: settingsSlice,
    },
    preloadedState: {
      settings: {
        settings: {
          id: 1,
          unitCost: 0.15,
          thresholdLimit: 10.0,
          currencyType: 'USD',
          unitType: 'Units',
          customCurrencyName: null,
          customUnitName: null,
          theme: 'default',
          fontStyle: 'default',
          notificationsEnabled: true,
          notificationTime: '18:00',
        },
        isLoading: false,
        error: null,
        availableCurrencies: ['USD', 'EUR', 'GBP', 'ZAR', 'NGN', 'Custom'],
        availableUnits: ['Units', 'KWh', 'Custom'],
        availableThemes: ['default', 'dark', 'blue', 'green', 'purple'],
        availableFonts: ['default', 'roboto', 'opensans', 'lato', 'montserrat'],
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      <ThemeProvider>
        {component}
      </ThemeProvider>
    </Provider>
  );
};

describe('ModernDial', () => {
  const defaultProps = {
    currentValue: 75,
    maxValue: 100,
    title: 'Current Units',
    unit: 'Units',
  };

  it('should render without crashing', () => {
    const { getByText } = renderWithProviders(<ModernDial {...defaultProps} />);
    
    expect(getByText('Current Units')).toBeTruthy();
    expect(getByText('75.0')).toBeTruthy();
    expect(getByText('Units')).toBeTruthy();
  });

  it('should display subtitle when provided', () => {
    const props = {
      ...defaultProps,
      subtitle: 'Usage since last: 5.0 Units',
    };
    
    const { getByText } = renderWithProviders(<ModernDial {...props} />);
    
    expect(getByText('Usage since last: 5.0 Units')).toBeTruthy();
  });

  it('should handle warning state correctly', () => {
    const props = {
      ...defaultProps,
      showWarning: true,
    };
    
    const { getByText } = renderWithProviders(<ModernDial {...props} />);
    
    // Should still render the value and title
    expect(getByText('75.0')).toBeTruthy();
    expect(getByText('Current Units')).toBeTruthy();
  });

  it('should handle different sizes', () => {
    const props = {
      ...defaultProps,
      size: 150,
    };
    
    const { getByText } = renderWithProviders(<ModernDial {...props} />);
    
    expect(getByText('Current Units')).toBeTruthy();
  });

  it('should handle minimum value correctly', () => {
    const props = {
      ...defaultProps,
      currentValue: 25,
      minValue: 20,
      maxValue: 100,
    };
    
    const { getByText } = renderWithProviders(<ModernDial {...props} />);
    
    expect(getByText('25.0')).toBeTruthy();
  });

  it('should handle edge case values', () => {
    const props = {
      ...defaultProps,
      currentValue: 0,
      maxValue: 100,
    };
    
    const { getByText } = renderWithProviders(<ModernDial {...props} />);
    
    expect(getByText('0.0')).toBeTruthy();
  });

  it('should handle values exceeding maximum', () => {
    const props = {
      ...defaultProps,
      currentValue: 150,
      maxValue: 100,
    };
    
    const { getByText } = renderWithProviders(<ModernDial {...props} />);
    
    expect(getByText('150.0')).toBeTruthy();
  });

  it('should format decimal values correctly', () => {
    const props = {
      ...defaultProps,
      currentValue: 75.567,
    };
    
    const { getByText } = renderWithProviders(<ModernDial {...props} />);
    
    expect(getByText('75.6')).toBeTruthy();
  });
});
