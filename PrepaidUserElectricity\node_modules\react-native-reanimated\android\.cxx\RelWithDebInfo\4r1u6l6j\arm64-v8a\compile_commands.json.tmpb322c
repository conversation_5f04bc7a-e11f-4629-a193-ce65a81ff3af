[
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\colgr\\Documents\\Augment-Projects\\In_Production_Builds\\Prepaid_User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\AndroidUIScheduler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\PlatformLogger.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsModule.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp"
},
{
  "directory": "C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/4r1u6l6j/arm64-v8a",
  "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Augment-Projects/In Production Builds/Prepaid User/PrepaidUserElectricity/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1c9225614e4d0ab975f35005d007a9db/transformed/react-android-0.80.0-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/e3847898c9ad159c30703260241a1547/transformed/hermes-android-0.80.0-release/prefab/modules/libhermes/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80   -DREANIMATED_VERSION=3.18.0 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DNDEBUG -DJS_RUNTIME_HERMES=1 -O2 -g -DNDEBUG -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsOnLoad.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp\"",
  "file": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\W