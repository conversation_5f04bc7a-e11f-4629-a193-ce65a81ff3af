1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.prepaiduserelectricity"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:5:5-68
12-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:5:22-65
13    <uses-permission android:name="android.permission.VIBRATE" />
13-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:6:5-66
13-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:6:22-63
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:7:5-80
14-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:7:22-78
15    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
15-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:8:5-79
15-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:8:22-76
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:9:5-77
16-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:9:22-74
17
18    <permission
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.prepaiduserelectricity.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.prepaiduserelectricity.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:11:5-35:19
25        android:name="com.prepaiduserelectricity.MainApplication"
25-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:12:7-38
26        android:allowBackup="false"
26-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:16:7-34
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
28        android:debuggable="true"
29        android:hardwareAccelerated="true"
29-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:20:7-41
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:14:7-41
31        android:label="@string/app_name"
31-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:13:7-39
32        android:largeHeap="true"
32-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:21:7-31
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:15:7-52
34        android:supportsRtl="true"
34-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:18:7-33
35        android:theme="@style/AppTheme"
35-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:17:7-38
36        android:usesCleartextTraffic="false" >
36-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:19:7-43
37        <activity
37-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:23:7-34:18
38            android:name="com.prepaiduserelectricity.MainActivity"
38-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:24:9-37
39            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
39-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:26:9-118
40            android:exported="true"
40-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:29:9-32
41            android:label="@string/app_name"
41-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:25:9-41
42            android:launchMode="singleTask"
42-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:27:9-40
43            android:windowSoftInputMode="adjustResize" >
43-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:28:9-51
44            <intent-filter>
44-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:30:9-33:25
45                <action android:name="android.intent.action.MAIN" />
45-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:31:13-65
45-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:31:21-62
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:32:13-73
47-->C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:32:23-70
48            </intent-filter>
49        </activity>
50
51        <provider
51-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.prepaiduserelectricity.androidx-startup"
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
61        </provider>
62    </application>
63
64</manifest>
