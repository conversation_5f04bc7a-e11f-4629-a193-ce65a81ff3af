{"version": 3, "file": "events.js", "names": ["createEventLogger", "events", "logEvent", "name", "event", "eventEntry", "payload", "push", "getEventsNames", "map", "lastEventPayload", "filter", "e", "pop"], "sources": ["../../src/test-utils/events.ts"], "sourcesContent": ["export interface EventEntry {\n  name: string;\n  payload: any;\n}\n\nexport function createEventLogger() {\n  const events: EventEntry[] = [];\n  const logEvent = (name: string) => {\n    return (event: unknown) => {\n      const eventEntry: EventEntry = {\n        name,\n        payload: event,\n      };\n\n      events.push(eventEntry);\n    };\n  };\n\n  return { events, logEvent };\n}\n\nexport function getEventsNames(events: EventEntry[]) {\n  return events.map((event) => event.name);\n}\n\nexport function lastEventPayload(events: EventEntry[], name: string) {\n  return events.filter((e) => e.name === name).pop()?.payload;\n}\n"], "mappings": ";;;;;;;;AAKO,SAASA,iBAAiBA,CAAA,EAAG;EAClC,MAAMC,MAAoB,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAIC,IAAY,IAAK;IACjC,OAAQC,KAAc,IAAK;MACzB,MAAMC,UAAsB,GAAG;QAC7BF,IAAI;QACJG,OAAO,EAAEF;MACX,CAAC;MAEDH,MAAM,CAACM,IAAI,CAACF,UAAU,CAAC;IACzB,CAAC;EACH,CAAC;EAED,OAAO;IAAEJ,MAAM;IAAEC;EAAS,CAAC;AAC7B;AAEO,SAASM,cAAcA,CAACP,MAAoB,EAAE;EACnD,OAAOA,MAAM,CAACQ,GAAG,CAAEL,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;AAC1C;AAEO,SAASO,gBAAgBA,CAACT,MAAoB,EAAEE,IAAY,EAAE;EACnE,OAAOF,MAAM,CAACU,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACT,IAAI,KAAKA,IAAI,CAAC,CAACU,GAAG,CAAC,CAAC,EAAEP,OAAO;AAC7D", "ignoreList": []}