{"version": 3, "file": "to-have-text-content.js", "names": ["_jestMatcherU<PERSON>s", "require", "_textContent", "_matches", "_utils", "toHaveTextContent", "element", "expectedText", "options", "checkHostElement", "text", "getTextContent", "pass", "matches", "normalizer", "exact", "message", "formatMessage", "matcherHint", "isNot", "join"], "sources": ["../../src/matchers/to-have-text-content.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\n\nimport { getTextContent } from '../helpers/text-content';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport { matches } from '../matches';\nimport { checkHostElement, formatMessage } from './utils';\n\nexport function toHaveTextContent(\n  this: jest.MatcherContext,\n  element: ReactTestInstance,\n  expectedText: TextMatch,\n  options?: TextMatchOptions,\n) {\n  checkHostElement(element, toHaveTextContent, this);\n\n  const text = getTextContent(element);\n\n  return {\n    pass: matches(expectedText, text, options?.normalizer, options?.exact),\n    message: () => {\n      return [\n        formatMessage(\n          matcherHint(`${this.isNot ? '.not' : ''}.toHaveTextContent`, 'element', ''),\n          `Expected element ${this.isNot ? 'not to' : 'to'} have text content`,\n          expectedText,\n          'Received',\n          text,\n        ),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEO,SAASI,iBAAiBA,CAE/BC,OAA0B,EAC1BC,YAAuB,EACvBC,OAA0B,EAC1B;EACA,IAAAC,uBAAgB,EAACH,OAAO,EAAED,iBAAiB,EAAE,IAAI,CAAC;EAElD,MAAMK,IAAI,GAAG,IAAAC,2BAAc,EAACL,OAAO,CAAC;EAEpC,OAAO;IACLM,IAAI,EAAE,IAAAC,gBAAO,EAACN,YAAY,EAAEG,IAAI,EAAEF,OAAO,EAAEM,UAAU,EAAEN,OAAO,EAAEO,KAAK,CAAC;IACtEC,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,oBAAa,EACX,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,oBAAoB,EAAE,SAAS,EAAE,EAAE,CAAC,EAC3E,oBAAoB,IAAI,CAACA,KAAK,GAAG,QAAQ,GAAG,IAAI,oBAAoB,EACpEZ,YAAY,EACZ,UAAU,EACVG,IACF,CAAC,CACF,CAACU,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}