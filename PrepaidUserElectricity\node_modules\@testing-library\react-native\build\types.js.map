{"version": 3, "file": "types.js", "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["/**\n * Location of an element.\n */\nexport interface Point {\n  y: number;\n  x: number;\n}\n\n/**\n * Size of an element.\n */\nexport interface Size {\n  height: number;\n  width: number;\n}\n\n// TS autocomplete trick\n// Ref: https://github.com/microsoft/TypeScript/issues/29729#issuecomment-567871939\nexport type StringWithAutocomplete<T> = T | (string & {});\n"], "mappings": "", "ignoreList": []}