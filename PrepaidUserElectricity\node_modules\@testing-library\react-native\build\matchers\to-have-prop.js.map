{"version": 3, "file": "to-have-prop.js", "names": ["_jestMatcherU<PERSON>s", "require", "_utils", "toHaveProp", "element", "name", "expectedValue", "checkHostElement", "isExpectedValueDefined", "undefined", "hasProp", "props", "receivedValue", "pass", "equals", "message", "to", "isNot", "matcher", "matcherHint", "printExpected", "secondArgument", "formatMessage", "formatProp", "value", "stringify"], "sources": ["../../src/matchers/to-have-prop.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, printExpected, stringify } from 'jest-matcher-utils';\n\nimport { checkHostElement, formatMessage } from './utils';\n\nexport function toHaveProp(\n  this: jest.MatcherContext,\n  element: ReactTestInstance,\n  name: string,\n  expectedValue: unknown,\n) {\n  checkHostElement(element, toHaveProp, this);\n\n  const isExpectedValueDefined = expectedValue !== undefined;\n  const hasProp = name in element.props;\n  const receivedValue = element.props[name];\n\n  const pass = isExpectedValueDefined\n    ? hasProp && this.equals(expectedValue, receivedValue)\n    : hasProp;\n\n  return {\n    pass,\n    message: () => {\n      const to = this.isNot ? 'not to' : 'to';\n      const matcher = matcherHint(\n        `${this.isNot ? '.not' : ''}.toHaveProp`,\n        'element',\n        printExpected(name),\n        {\n          secondArgument: isExpectedValueDefined ? printExpected(expectedValue) : undefined,\n        },\n      );\n      return formatMessage(\n        matcher,\n        `Expected element ${to} have prop`,\n        formatProp(name, expectedValue),\n        'Received',\n        hasProp ? formatProp(name, receivedValue) : undefined,\n      );\n    },\n  };\n}\n\nfunction formatProp(name: string, value: unknown) {\n  if (value === undefined) {\n    return name;\n  }\n\n  if (typeof value === 'string') {\n    return `${name}=\"${value}\"`;\n  }\n\n  return `${name}={${stringify(value)}}`;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAEO,SAASE,UAAUA,CAExBC,OAA0B,EAC1BC,IAAY,EACZC,aAAsB,EACtB;EACA,IAAAC,uBAAgB,EAACH,OAAO,EAAED,UAAU,EAAE,IAAI,CAAC;EAE3C,MAAMK,sBAAsB,GAAGF,aAAa,KAAKG,SAAS;EAC1D,MAAMC,OAAO,GAAGL,IAAI,IAAID,OAAO,CAACO,KAAK;EACrC,MAAMC,aAAa,GAAGR,OAAO,CAACO,KAAK,CAACN,IAAI,CAAC;EAEzC,MAAMQ,IAAI,GAAGL,sBAAsB,GAC/BE,OAAO,IAAI,IAAI,CAACI,MAAM,CAACR,aAAa,EAAEM,aAAa,CAAC,GACpDF,OAAO;EAEX,OAAO;IACLG,IAAI;IACJE,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,QAAQ,GAAG,IAAI;MACvC,MAAMC,OAAO,GAAG,IAAAC,6BAAW,EACzB,GAAG,IAAI,CAACF,KAAK,GAAG,MAAM,GAAG,EAAE,aAAa,EACxC,SAAS,EACT,IAAAG,+BAAa,EAACf,IAAI,CAAC,EACnB;QACEgB,cAAc,EAAEb,sBAAsB,GAAG,IAAAY,+BAAa,EAACd,aAAa,CAAC,GAAGG;MAC1E,CACF,CAAC;MACD,OAAO,IAAAa,oBAAa,EAClBJ,OAAO,EACP,oBAAoBF,EAAE,YAAY,EAClCO,UAAU,CAAClB,IAAI,EAAEC,aAAa,CAAC,EAC/B,UAAU,EACVI,OAAO,GAAGa,UAAU,CAAClB,IAAI,EAAEO,aAAa,CAAC,GAAGH,SAC9C,CAAC;IACH;EACF,CAAC;AACH;AAEA,SAASc,UAAUA,CAAClB,IAAY,EAAEmB,KAAc,EAAE;EAChD,IAAIA,KAAK,KAAKf,SAAS,EAAE;IACvB,OAAOJ,IAAI;EACb;EAEA,IAAI,OAAOmB,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,GAAGnB,IAAI,KAAKmB,KAAK,GAAG;EAC7B;EAEA,OAAO,GAAGnB,IAAI,KAAK,IAAAoB,2BAAS,EAACD,KAAK,CAAC,GAAG;AACxC", "ignoreList": []}