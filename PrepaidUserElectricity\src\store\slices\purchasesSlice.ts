import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import DatabaseManager, { Purchase } from '../../database/DatabaseManager';

export interface PurchasesState {
  purchases: Purchase[];
  isLoading: boolean;
  error: string | null;
  weeklyTotal: number;
  monthlyTotal: number;
}

const initialState: PurchasesState = {
  purchases: [],
  isLoading: false,
  error: null,
  weeklyTotal: 0,
  monthlyTotal: 0,
};

// Async thunks
export const loadPurchases = createAsyncThunk(
  'purchases/loadPurchases',
  async (_, { rejectWithValue }) => {
    try {
      const purchases = await DatabaseManager.getPurchases();
      return purchases;
    } catch (error) {
      return rejectWithValue('Failed to load purchases');
    }
  }
);

export const addPurchase = createAsyncThunk(
  'purchases/addPurchase',
  async (purchase: Omit<Purchase, 'id'>, { rejectWithValue }) => {
    try {
      const purchaseId = await DatabaseManager.addPurchase(purchase);
      const purchases = await DatabaseManager.getPurchases();
      return { purchases, purchaseId };
    } catch (error) {
      return rejectWithValue('Failed to add purchase');
    }
  }
);

export const loadPurchaseTotals = createAsyncThunk(
  'purchases/loadTotals',
  async (_, { rejectWithValue }) => {
    try {
      const weeklyTotals = await DatabaseManager.getWeeklyTotals();
      const monthlyTotals = await DatabaseManager.getMonthlyTotals();
      return {
        weekly: weeklyTotals.purchases,
        monthly: monthlyTotals.purchases,
      };
    } catch (error) {
      return rejectWithValue('Failed to load purchase totals');
    }
  }
);

const purchasesSlice = createSlice({
  name: 'purchases',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setPurchases: (state, action: PayloadAction<Purchase[]>) => {
      state.purchases = action.payload;
    },
    updatePurchaseTotals: (state, action: PayloadAction<{ weekly: number; monthly: number }>) => {
      state.weeklyTotal = action.payload.weekly;
      state.monthlyTotal = action.payload.monthly;
    },
  },
  extraReducers: (builder) => {
    builder
      // Load purchases
      .addCase(loadPurchases.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadPurchases.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchases = action.payload;
      })
      .addCase(loadPurchases.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Add purchase
      .addCase(addPurchase.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addPurchase.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchases = action.payload.purchases;
      })
      .addCase(addPurchase.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Load totals
      .addCase(loadPurchaseTotals.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadPurchaseTotals.fulfilled, (state, action) => {
        state.isLoading = false;
        state.weeklyTotal = action.payload.weekly;
        state.monthlyTotal = action.payload.monthly;
      })
      .addCase(loadPurchaseTotals.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setPurchases, updatePurchaseTotals } = purchasesSlice.actions;

export default purchasesSlice.reducer;
