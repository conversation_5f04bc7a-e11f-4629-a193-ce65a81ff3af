-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62a6d5f4699f9a58c84c13e5faffb8be\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ad2aac83c19b426b8863cac0dadd4f81\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5032a7fcbae444f590085e539f48eacb\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74b37bc7dd1bf2c703c44454fb275d4d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\57eca03c7570c6a32fde632262858e35\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fa30ea4eb3b318cffe211758f940f065\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da865d47249ca119cd8be39823372d40\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1897ce85bdd8175830f8389991c589a3\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f10e81b73e68909755e2db17a41bd969\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a9ccd01c589e8c01365e7795cb7a12d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fde58cf2c55148be58f274993d9bc46c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5526b632238e88d640ceba5f7845e93\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c51b26662b69c05ac7c5dc24bb2f8029\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd777eecaac59f53876f30dc7878baec\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39315c71a32c12252ad29e6a112d1b30\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1947eb72b8780cc9640ed415445441f6\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2e31e07d61c20a8b031269a2c42cacc\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\26a626642aaf390e40c4acb7e0df281c\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\806114a696332446f5f2e1edb02b1e89\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eeec84f6596876926c84e242081f51c5\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\30eb811f563f15bfecd56a5b6cdb0219\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53b39b5212b8a62f753bf207e5508589\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\42de018a6ccdb351bf97a1a6517e7bd5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1dceed779367f6eb60b74d5f19954785\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6580da11ad55d5ef40ab6970ecf3ead5\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f480778d6bc389503766ef0e9bdc81bd\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afab2c424e75d1831bb1c0ef1eca7646\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae8c0d208c8aed8e4346f3ca7505e6bc\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64c2fb7784881ff498d4e7c72858ad5e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29d8d0a75dfa20010bea38cf94dd3268\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\733c8f002c61d579cd7046c57cbbc81d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f13302da2444591178af56ad1b323a6a\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\95a52c4c8076f3c287c4ff60a1041b42\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6054221308497d4af0cc74a146a0a1e\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5c79d4892777bbe8770c2d02918d3eee\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5175a4a7f61b8c95aef91228e7aebfdc\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27ad18af0154f283ccc30e37295b8f3b\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
	package
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:1-36:12
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:5:5-68
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:5:22-65
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:6:5-66
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:6:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:9:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:9:22-74
application
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:11:5-35:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62a6d5f4699f9a58c84c13e5faffb8be\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62a6d5f4699f9a58c84c13e5faffb8be\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ad2aac83c19b426b8863cac0dadd4f81\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ad2aac83c19b426b8863cac0dadd4f81\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afab2c424e75d1831bb1c0ef1eca7646\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afab2c424e75d1831bb1c0ef1eca7646\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64c2fb7784881ff498d4e7c72858ad5e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64c2fb7784881ff498d4e7c72858ad5e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:18:7-33
	android:label
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:13:7-39
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:20:7-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:15:7-52
	android:largeHeap
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:21:7-31
	android:icon
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:14:7-41
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:16:7-34
	android:theme
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:17:7-38
	tools:replace
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:22:7-51
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:19:7-43
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:12:7-38
activity#com.prepaiduserelectricity.MainActivity
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:23:7-34:18
	android:label
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:25:9-41
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:27:9-40
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:28:9-51
	android:exported
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:29:9-32
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:26:9-118
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:24:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:30:9-33:25
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:31:13-65
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:31:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:32:13-73
	android:name
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml:32:23-70
uses-sdk
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62a6d5f4699f9a58c84c13e5faffb8be\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62a6d5f4699f9a58c84c13e5faffb8be\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ad2aac83c19b426b8863cac0dadd4f81\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ad2aac83c19b426b8863cac0dadd4f81\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5032a7fcbae444f590085e539f48eacb\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5032a7fcbae444f590085e539f48eacb\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74b37bc7dd1bf2c703c44454fb275d4d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\74b37bc7dd1bf2c703c44454fb275d4d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\57eca03c7570c6a32fde632262858e35\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\57eca03c7570c6a32fde632262858e35\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fa30ea4eb3b318cffe211758f940f065\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fa30ea4eb3b318cffe211758f940f065\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da865d47249ca119cd8be39823372d40\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da865d47249ca119cd8be39823372d40\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1897ce85bdd8175830f8389991c589a3\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1897ce85bdd8175830f8389991c589a3\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f10e81b73e68909755e2db17a41bd969\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f10e81b73e68909755e2db17a41bd969\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a9ccd01c589e8c01365e7795cb7a12d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a9ccd01c589e8c01365e7795cb7a12d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fde58cf2c55148be58f274993d9bc46c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fde58cf2c55148be58f274993d9bc46c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5526b632238e88d640ceba5f7845e93\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5526b632238e88d640ceba5f7845e93\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c51b26662b69c05ac7c5dc24bb2f8029\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c51b26662b69c05ac7c5dc24bb2f8029\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd777eecaac59f53876f30dc7878baec\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd777eecaac59f53876f30dc7878baec\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39315c71a32c12252ad29e6a112d1b30\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39315c71a32c12252ad29e6a112d1b30\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1947eb72b8780cc9640ed415445441f6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1947eb72b8780cc9640ed415445441f6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2e31e07d61c20a8b031269a2c42cacc\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2e31e07d61c20a8b031269a2c42cacc\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\26a626642aaf390e40c4acb7e0df281c\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\26a626642aaf390e40c4acb7e0df281c\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\806114a696332446f5f2e1edb02b1e89\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\806114a696332446f5f2e1edb02b1e89\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eeec84f6596876926c84e242081f51c5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eeec84f6596876926c84e242081f51c5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\30eb811f563f15bfecd56a5b6cdb0219\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\30eb811f563f15bfecd56a5b6cdb0219\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53b39b5212b8a62f753bf207e5508589\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53b39b5212b8a62f753bf207e5508589\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\42de018a6ccdb351bf97a1a6517e7bd5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\42de018a6ccdb351bf97a1a6517e7bd5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1dceed779367f6eb60b74d5f19954785\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1dceed779367f6eb60b74d5f19954785\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6580da11ad55d5ef40ab6970ecf3ead5\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6580da11ad55d5ef40ab6970ecf3ead5\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f480778d6bc389503766ef0e9bdc81bd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f480778d6bc389503766ef0e9bdc81bd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afab2c424e75d1831bb1c0ef1eca7646\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afab2c424e75d1831bb1c0ef1eca7646\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae8c0d208c8aed8e4346f3ca7505e6bc\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae8c0d208c8aed8e4346f3ca7505e6bc\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64c2fb7784881ff498d4e7c72858ad5e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64c2fb7784881ff498d4e7c72858ad5e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29d8d0a75dfa20010bea38cf94dd3268\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29d8d0a75dfa20010bea38cf94dd3268\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\733c8f002c61d579cd7046c57cbbc81d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\733c8f002c61d579cd7046c57cbbc81d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f13302da2444591178af56ad1b323a6a\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f13302da2444591178af56ad1b323a6a\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\95a52c4c8076f3c287c4ff60a1041b42\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\95a52c4c8076f3c287c4ff60a1041b42\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6054221308497d4af0cc74a146a0a1e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6054221308497d4af0cc74a146a0a1e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5c79d4892777bbe8770c2d02918d3eee\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5c79d4892777bbe8770c2d02918d3eee\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5175a4a7f61b8c95aef91228e7aebfdc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5175a4a7f61b8c95aef91228e7aebfdc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27ad18af0154f283ccc30e37295b8f3b\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27ad18af0154f283ccc30e37295b8f3b\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid User\PrepaidUserElectricity\android\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afab2c424e75d1831bb1c0ef1eca7646\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afab2c424e75d1831bb1c0ef1eca7646\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e37c89f77a8b800433a8f81e8cfaf0e\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.prepaiduserelectricity.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.prepaiduserelectricity.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\32793288452e4e46b72ff0226ea01ae3\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f215eab41fcf8cb12add42e089f8ca3\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
