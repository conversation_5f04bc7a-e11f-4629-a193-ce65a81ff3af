{"version": 3, "file": "text-input.js", "names": ["_nativeState", "require", "_hostComponentNames", "isEditableTextInput", "element", "isHostTextInput", "props", "editable", "getTextInputValue", "Error", "type", "value", "nativeState", "valueForElement", "get", "defaultValue"], "sources": ["../../src/helpers/text-input.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { nativeState } from '../native-state';\nimport { isHostTextInput } from './host-component-names';\n\nexport function isEditableTextInput(element: ReactTestInstance) {\n  return isHostTextInput(element) && element.props.editable !== false;\n}\n\nexport function getTextInputValue(element: ReactTestInstance) {\n  if (!isHostTextInput(element)) {\n    throw new Error(`Element is not a \"TextInput\", but it has type \"${element.type}\".`);\n  }\n\n  return (\n    element.props.value ??\n    nativeState.valueForElement.get(element) ??\n    element.props.defaultValue ??\n    ''\n  );\n}\n"], "mappings": ";;;;;;;AAEA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AAEO,SAASE,mBAAmBA,CAACC,OAA0B,EAAE;EAC9D,OAAO,IAAAC,mCAAe,EAACD,OAAO,CAAC,IAAIA,OAAO,CAACE,KAAK,CAACC,QAAQ,KAAK,KAAK;AACrE;AAEO,SAASC,iBAAiBA,CAACJ,OAA0B,EAAE;EAC5D,IAAI,CAAC,IAAAC,mCAAe,EAACD,OAAO,CAAC,EAAE;IAC7B,MAAM,IAAIK,KAAK,CAAC,kDAAkDL,OAAO,CAACM,IAAI,IAAI,CAAC;EACrF;EAEA,OACEN,OAAO,CAACE,KAAK,CAACK,KAAK,IACnBC,wBAAW,CAACC,eAAe,CAACC,GAAG,CAACV,OAAO,CAAC,IACxCA,OAAO,CAACE,KAAK,CAACS,YAAY,IAC1B,EAAE;AAEN", "ignoreList": []}