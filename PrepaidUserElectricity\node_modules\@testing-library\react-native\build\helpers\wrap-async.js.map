{"version": 3, "file": "wrap-async.js", "names": ["_act", "require", "_flushMicroTasks", "wrapAsync", "callback", "previousActEnvironment", "getIsReactActEnvironment", "setReactActEnvironment", "result", "flushMicroTasks"], "sources": ["../../src/helpers/wrap-async.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nimport { getIsReactActEnvironment, setReactActEnvironment } from '../act';\nimport { flushMicroTasks } from '../flush-micro-tasks';\n\n/**\n * Run given async callback with temporarily disabled `act` environment and flushes microtasks queue.\n *\n * @param callback Async callback to run\n * @returns Result of the callback\n */\nexport async function wrapAsync<Result>(callback: () => Promise<Result>): Promise<Result> {\n  const previousActEnvironment = getIsReactActEnvironment();\n  setReactActEnvironment(false);\n\n  try {\n    const result = await callback();\n    // Flush the microtask queue before restoring the `act` environment\n    await flushMicroTasks();\n    return result;\n  } finally {\n    setReactActEnvironment(previousActEnvironment);\n  }\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AAHA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeE,SAASA,CAASC,QAA+B,EAAmB;EACxF,MAAMC,sBAAsB,GAAG,IAAAC,6BAAwB,EAAC,CAAC;EACzD,IAAAC,2BAAsB,EAAC,KAAK,CAAC;EAE7B,IAAI;IACF,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAAC,CAAC;IAC/B;IACA,MAAM,IAAAK,gCAAe,EAAC,CAAC;IACvB,OAAOD,MAAM;EACf,CAAC,SAAS;IACR,IAAAD,2BAAsB,EAACF,sBAAsB,CAAC;EAChD;AACF", "ignoreList": []}