@echo off
"C:\\Program Files\\Android\\Android Studio\\jbr\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  arm64-v8a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging709633325986615266\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1c9225614e4d0ab975f35005d007a9db\\transformed\\react-android-0.80.0-release\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e3847898c9ad159c30703260241a1547\\transformed\\hermes-android-0.80.0-release\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3ba4db1ae352eb90610ca685a00c889a\\transformed\\fbjni-0.7.0\\prefab"
