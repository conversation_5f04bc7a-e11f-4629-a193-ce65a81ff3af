{"buildFiles": ["C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\hermes-engine\\hermes-engineConfig.cmake", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\hermes-engine\\hermes-engineConfigVersion.cmake", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\4r1u6l6j\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"reanimated::@89a6a9b85fb42923616c": {"toolchain": "toolchain", "abi": "x86", "artifactName": "reanimated", "output": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\4r1u6l6j\\obj\\x86\\libreanimated.so", "runtimeFiles": ["C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\4r1u6l6j\\obj\\x86\\libworklets.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1c9225614e4d0ab975f35005d007a9db\\transformed\\react-android-0.80.0-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1c9225614e4d0ab975f35005d007a9db\\transformed\\react-android-0.80.0-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3ba4db1ae352eb90610ca685a00c889a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1c9225614e4d0ab975f35005d007a9db\\transformed\\react-android-0.80.0-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e3847898c9ad159c30703260241a1547\\transformed\\hermes-android-0.80.0-release\\prefab\\modules\\libhermes\\libs\\android.x86\\libhermes.so"]}, "worklets::@a0394df2d94e5212d8bd": {"toolchain": "toolchain", "abi": "x86", "artifactName": "worklets", "output": "C:\\Users\\<USER>\\Documents\\Augment-Projects\\In Production Builds\\Prepaid User\\PrepaidUserElectricity\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\4r1u6l6j\\obj\\x86\\libworklets.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1c9225614e4d0ab975f35005d007a9db\\transformed\\react-android-0.80.0-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3ba4db1ae352eb90610ca685a00c889a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1c9225614e4d0ab975f35005d007a9db\\transformed\\react-android-0.80.0-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e3847898c9ad159c30703260241a1547\\transformed\\hermes-android-0.80.0-release\\prefab\\modules\\libhermes\\libs\\android.x86\\libhermes.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}