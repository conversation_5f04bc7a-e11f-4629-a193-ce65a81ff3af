{"version": 3, "file": "placeholder-text.js", "names": ["_findAll", "require", "_hostComponentNames", "_matches", "_makeQueries", "matchPlaceholderText", "node", "placeholder", "options", "exact", "normalizer", "matches", "props", "queryAllByPlaceholderText", "instance", "queryAllByPlaceholderFn", "queryOptions", "findAll", "isHostTextInput", "getMultipleError", "String", "getMissingError", "get<PERSON>y", "getAllBy", "queryBy", "queryAllBy", "find<PERSON><PERSON>", "findAllBy", "makeQueries", "bindByPlaceholderTextQueries", "getByPlaceholderText", "getAllByPlaceholderText", "queryByPlaceholderText", "findByPlaceholderText", "findAllByPlaceholderText", "exports"], "sources": ["../../src/queries/placeholder-text.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { findAll } from '../helpers/find-all';\nimport { isHostTextInput } from '../helpers/host-component-names';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport { matches } from '../matches';\nimport type {\n  FindAllByQuery,\n  FindByQuery,\n  GetAllByQuery,\n  GetByQuery,\n  QueryAllByQuery,\n  QueryByQuery,\n} from './make-queries';\nimport { makeQueries } from './make-queries';\nimport type { CommonQueryOptions } from './options';\n\ntype ByPlaceholderTextOptions = CommonQueryOptions & TextMatchOptions;\n\nconst matchPlaceholderText = (\n  node: ReactTestInstance,\n  placeholder: TextMatch,\n  options: TextMatchOptions = {},\n) => {\n  const { exact, normalizer } = options;\n  return matches(placeholder, node.props.placeholder, normalizer, exact);\n};\n\nconst queryAllByPlaceholderText = (\n  instance: ReactTestInstance,\n): QueryAllByQuery<TextMatch, ByPlaceholderTextOptions> =>\n  function queryAllByPlaceholderFn(placeholder, queryOptions) {\n    return findAll(\n      instance,\n      (node) => isHostTextInput(node) && matchPlaceholderText(node, placeholder, queryOptions),\n      queryOptions,\n    );\n  };\n\nconst getMultipleError = (placeholder: TextMatch) =>\n  `Found multiple elements with placeholder: ${String(placeholder)} `;\nconst getMissingError = (placeholder: TextMatch) =>\n  `Unable to find an element with placeholder: ${String(placeholder)}`;\n\nconst { getBy, getAllBy, queryBy, queryAllBy, findBy, findAllBy } = makeQueries(\n  queryAllByPlaceholderText,\n  getMissingError,\n  getMultipleError,\n);\n\nexport type ByPlaceholderTextQueries = {\n  getByPlaceholderText: GetByQuery<TextMatch, ByPlaceholderTextOptions>;\n  getAllByPlaceholderText: GetAllByQuery<TextMatch, ByPlaceholderTextOptions>;\n  queryByPlaceholderText: QueryByQuery<TextMatch, ByPlaceholderTextOptions>;\n  queryAllByPlaceholderText: QueryAllByQuery<TextMatch, ByPlaceholderTextOptions>;\n  findByPlaceholderText: FindByQuery<TextMatch, ByPlaceholderTextOptions>;\n  findAllByPlaceholderText: FindAllByQuery<TextMatch, ByPlaceholderTextOptions>;\n};\n\nexport const bindByPlaceholderTextQueries = (\n  instance: ReactTestInstance,\n): ByPlaceholderTextQueries => ({\n  getByPlaceholderText: getBy(instance),\n  getAllByPlaceholderText: getAllBy(instance),\n  queryByPlaceholderText: queryBy(instance),\n  queryAllByPlaceholderText: queryAllBy(instance),\n  findByPlaceholderText: findBy(instance),\n  findAllByPlaceholderText: findAllBy(instance),\n});\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAF,OAAA;AASA,IAAAG,YAAA,GAAAH,OAAA;AAKA,MAAMI,oBAAoB,GAAGA,CAC3BC,IAAuB,EACvBC,WAAsB,EACtBC,OAAyB,GAAG,CAAC,CAAC,KAC3B;EACH,MAAM;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGF,OAAO;EACrC,OAAO,IAAAG,gBAAO,EAACJ,WAAW,EAAED,IAAI,CAACM,KAAK,CAACL,WAAW,EAAEG,UAAU,EAAED,KAAK,CAAC;AACxE,CAAC;AAED,MAAMI,yBAAyB,GAC7BC,QAA2B,IAE3B,SAASC,uBAAuBA,CAACR,WAAW,EAAES,YAAY,EAAE;EAC1D,OAAO,IAAAC,gBAAO,EACZH,QAAQ,EACPR,IAAI,IAAK,IAAAY,mCAAe,EAACZ,IAAI,CAAC,IAAID,oBAAoB,CAACC,IAAI,EAAEC,WAAW,EAAES,YAAY,CAAC,EACxFA,YACF,CAAC;AACH,CAAC;AAEH,MAAMG,gBAAgB,GAAIZ,WAAsB,IAC9C,6CAA6Ca,MAAM,CAACb,WAAW,CAAC,GAAG;AACrE,MAAMc,eAAe,GAAId,WAAsB,IAC7C,+CAA+Ca,MAAM,CAACb,WAAW,CAAC,EAAE;AAEtE,MAAM;EAAEe,KAAK;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAU,CAAC,GAAG,IAAAC,wBAAW,EAC7Ef,yBAAyB,EACzBQ,eAAe,EACfF,gBACF,CAAC;AAWM,MAAMU,4BAA4B,GACvCf,QAA2B,KACG;EAC9BgB,oBAAoB,EAAER,KAAK,CAACR,QAAQ,CAAC;EACrCiB,uBAAuB,EAAER,QAAQ,CAACT,QAAQ,CAAC;EAC3CkB,sBAAsB,EAAER,OAAO,CAACV,QAAQ,CAAC;EACzCD,yBAAyB,EAAEY,UAAU,CAACX,QAAQ,CAAC;EAC/CmB,qBAAqB,EAAEP,MAAM,CAACZ,QAAQ,CAAC;EACvCoB,wBAAwB,EAAEP,SAAS,CAACb,QAAQ;AAC9C,CAAC,CAAC;AAACqB,OAAA,CAAAN,4BAAA,GAAAA,4BAAA", "ignoreList": []}