{"version": 3, "file": "to-have-display-value.js", "names": ["_jestMatcherU<PERSON>s", "require", "_errors", "_hostComponentNames", "_textInput", "_matches", "_utils", "toHaveDisplayValue", "element", "expectedValue", "options", "checkHostElement", "isHostTextInput", "ErrorWithStack", "type", "receivedValue", "getTextInputValue", "pass", "matches", "normalizer", "exact", "message", "formatMessage", "matcherHint", "isNot", "join"], "sources": ["../../src/matchers/to-have-display-value.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\n\nimport { ErrorWithStack } from '../helpers/errors';\nimport { isHostTextInput } from '../helpers/host-component-names';\nimport { getTextInputValue } from '../helpers/text-input';\nimport type { TextMatch, TextMatchOptions } from '../matches';\nimport { matches } from '../matches';\nimport { checkHostElement, formatMessage } from './utils';\n\nexport function toHaveDisplayValue(\n  this: jest.MatcherContext,\n  element: ReactTestInstance,\n  expectedValue: TextMatch,\n  options?: TextMatchOptions,\n) {\n  checkHostElement(element, toHaveDisplayValue, this);\n\n  if (!isHostTextInput(element)) {\n    throw new ErrorWithStack(\n      `toHaveDisplayValue() works only with host \"TextInput\" elements. Passed element has type \"${element.type}\".`,\n      toHaveDisplayValue,\n    );\n  }\n\n  const receivedValue = getTextInputValue(element);\n\n  return {\n    pass: matches(expectedValue, receivedValue, options?.normalizer, options?.exact),\n    message: () => {\n      return [\n        formatMessage(\n          matcherHint(`${this.isNot ? '.not' : ''}.toHaveDisplayValue`, 'element', ''),\n          `Expected element ${this.isNot ? 'not to' : 'to'} have display value`,\n          expectedValue,\n          'Received',\n          receivedValue,\n        ),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAEO,SAASM,kBAAkBA,CAEhCC,OAA0B,EAC1BC,aAAwB,EACxBC,OAA0B,EAC1B;EACA,IAAAC,uBAAgB,EAACH,OAAO,EAAED,kBAAkB,EAAE,IAAI,CAAC;EAEnD,IAAI,CAAC,IAAAK,mCAAe,EAACJ,OAAO,CAAC,EAAE;IAC7B,MAAM,IAAIK,sBAAc,CACtB,4FAA4FL,OAAO,CAACM,IAAI,IAAI,EAC5GP,kBACF,CAAC;EACH;EAEA,MAAMQ,aAAa,GAAG,IAAAC,4BAAiB,EAACR,OAAO,CAAC;EAEhD,OAAO;IACLS,IAAI,EAAE,IAAAC,gBAAO,EAACT,aAAa,EAAEM,aAAa,EAAEL,OAAO,EAAES,UAAU,EAAET,OAAO,EAAEU,KAAK,CAAC;IAChFC,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,oBAAa,EACX,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,qBAAqB,EAAE,SAAS,EAAE,EAAE,CAAC,EAC5E,oBAAoB,IAAI,CAACA,KAAK,GAAG,QAAQ,GAAG,IAAI,qBAAqB,EACrEf,aAAa,EACb,UAAU,EACVM,aACF,CAAC,CACF,CAACU,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}