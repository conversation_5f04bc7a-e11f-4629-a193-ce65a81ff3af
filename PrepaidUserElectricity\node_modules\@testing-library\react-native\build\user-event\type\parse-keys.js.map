{"version": 3, "file": "parse-keys.js", "names": ["knownKeys", "Set", "parse<PERSON>eys", "text", "result", "remainingText", "token", "rest", "getNextToken", "length", "has", "Error", "push", "slice", "endIndex", "indexOf"], "sources": ["../../../src/user-event/type/parse-keys.ts"], "sourcesContent": ["const knownKeys = new Set(['Enter', 'Backspace']);\n\nexport function parseKeys(text: string) {\n  const result = [];\n\n  let remainingText = text;\n  while (remainingText) {\n    const [token, rest] = getNextToken(remainingText);\n    if (token.length > 1 && !knownKeys.has(token)) {\n      throw new Error(`Unknown key \"${token}\" in \"${text}\"`);\n    }\n\n    result.push(token);\n    remainingText = rest;\n  }\n\n  return result;\n}\n\nfunction getNextToken(text: string): [string, string] {\n  // Detect `{{` => escaped `{`\n  if (text[0] === '{' && text[1] === '{') {\n    return ['{', text.slice(2)];\n  }\n\n  // Detect `{key}` => special key\n  if (text[0] === '{') {\n    const endIndex = text.indexOf('}');\n    if (endIndex === -1) {\n      throw new Error(`Invalid key sequence \"${text}\"`);\n    }\n\n    return [text.slice(1, endIndex), text.slice(endIndex + 1)];\n  }\n\n  if (text[0] === '\\n') {\n    return ['Enter', text.slice(1)];\n  }\n\n  return [text[0], text.slice(1)];\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAE1C,SAASC,SAASA,CAACC,IAAY,EAAE;EACtC,MAAMC,MAAM,GAAG,EAAE;EAEjB,IAAIC,aAAa,GAAGF,IAAI;EACxB,OAAOE,aAAa,EAAE;IACpB,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGC,YAAY,CAACH,aAAa,CAAC;IACjD,IAAIC,KAAK,CAACG,MAAM,GAAG,CAAC,IAAI,CAACT,SAAS,CAACU,GAAG,CAACJ,KAAK,CAAC,EAAE;MAC7C,MAAM,IAAIK,KAAK,CAAC,gBAAgBL,KAAK,SAASH,IAAI,GAAG,CAAC;IACxD;IAEAC,MAAM,CAACQ,IAAI,CAACN,KAAK,CAAC;IAClBD,aAAa,GAAGE,IAAI;EACtB;EAEA,OAAOH,MAAM;AACf;AAEA,SAASI,YAAYA,CAACL,IAAY,EAAoB;EACpD;EACA,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACtC,OAAO,CAAC,GAAG,EAAEA,IAAI,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7B;;EAEA;EACA,IAAIV,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACnB,MAAMW,QAAQ,GAAGX,IAAI,CAACY,OAAO,CAAC,GAAG,CAAC;IAClC,IAAID,QAAQ,KAAK,CAAC,CAAC,EAAE;MACnB,MAAM,IAAIH,KAAK,CAAC,yBAAyBR,IAAI,GAAG,CAAC;IACnD;IAEA,OAAO,CAACA,IAAI,CAACU,KAAK,CAAC,CAAC,EAAEC,QAAQ,CAAC,EAAEX,IAAI,CAACU,KAAK,CAACC,QAAQ,GAAG,CAAC,CAAC,CAAC;EAC5D;EAEA,IAAIX,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACpB,OAAO,CAAC,OAAO,EAAEA,IAAI,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC;EACjC;EAEA,OAAO,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC", "ignoreList": []}