{"modules": {"NativeReanimatedModule": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "installTurboModule", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "BooleanTypeAnnotation"}, "params": []}}]}, "moduleName": "ReanimatedModule"}, "NativeWorkletsModule": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "installTurboModule", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "BooleanTypeAnnotation"}, "params": [{"name": "valueUnpackerCode", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}]}, "moduleName": "WorkletsModule"}}}